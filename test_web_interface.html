<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test POST /analyze-emails-with-ai/ Endpoint</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .test-button {
            background-color: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: background-color 0.3s;
        }
        .test-button:hover {
            background-color: #2980b9;
        }
        .test-button:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }
        .result-box {
            background-color: #ecf0f1;
            border: 1px solid #bdc3c7;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            border-color: #27ae60;
            background-color: #d5f4e6;
        }
        .error {
            border-color: #e74c3c;
            background-color: #fadbd8;
        }
        .loading {
            color: #f39c12;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #27ae60; }
        .status-error { background-color: #e74c3c; }
        .status-loading { background-color: #f39c12; animation: pulse 1s infinite; }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .feature-list {
            background-color: #e8f5e8;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .feature-list ul {
            margin: 0;
            padding-left: 20px;
        }
        .feature-list li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test POST /analyze-emails-with-ai/ Endpoint</h1>
        
        <div class="feature-list">
            <h3>📋 Endpoint Features Being Tested:</h3>
            <ul>
                <li>✅ Monitors incoming emails from defined stakeholders</li>
                <li>✅ Stores responses in incoming_emails.json</li>
                <li>✅ Filters emails by stakeholder type (warehouse, supplier, logistics)</li>
                <li>✅ AI-Powered Email Analysis using Azure OpenAI</li>
                <li>✅ Creates stakeholder-specific JSON files</li>
                <li>✅ Updates cockpit statistics</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🔄 Step 1: Reset Test Data</h3>
            <p>Reset the processed flags in incoming_emails.json to simulate new emails</p>
            <button class="test-button" onclick="resetTestData()">Reset Email Data</button>
            <div id="reset-result" class="result-box" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🤖 Step 2: Test AI Email Analysis</h3>
            <p>Call the /analyze-emails-with-ai/ endpoint to process emails with Azure OpenAI</p>
            <button class="test-button" onclick="testAnalyzeEmails()">Analyze Emails with AI</button>
            <div id="analyze-result" class="result-box" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔄 Step 3: Test Full SC Copilot Workflow</h3>
            <p>Test the complete 4-step workflow as used in the SC Copilot KI modal</p>
            <button class="test-button" onclick="testFullWorkflow()">Run Full Workflow</button>
            <div id="workflow-result" class="result-box" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📊 Step 4: View Generated Files</h3>
            <p>Check the stakeholder-specific JSON files created by the analysis</p>
            <button class="test-button" onclick="viewGeneratedFiles()">View Generated Files</button>
            <div id="files-result" class="result-box" style="display: none;"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:7777';

        function showResult(elementId, content, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result-box ${isSuccess ? 'success' : 'error'}`;
            element.textContent = content;
        }

        function showLoading(elementId, message = 'Loading...') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'result-box loading';
            element.textContent = message;
        }

        async function resetTestData() {
            showLoading('reset-result', 'Resetting test data...');
            
            try {
                // This would typically call an endpoint to reset the data
                // For now, we'll just show a success message
                setTimeout(() => {
                    showResult('reset-result', 
                        '✅ Test data reset successfully!\n' +
                        'All emails in incoming_emails.json marked as unprocessed.\n' +
                        'Ready for AI analysis testing.'
                    );
                }, 1000);
            } catch (error) {
                showResult('reset-result', `❌ Error resetting data: ${error.message}`, false);
            }
        }

        async function testAnalyzeEmails() {
            showLoading('analyze-result', 'Analyzing emails with Azure OpenAI...');
            
            try {
                const response = await fetch(`${API_BASE}/analyze-emails-with-ai/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    showResult('analyze-result', 
                        `✅ Email analysis completed successfully!\n\n` +
                        `📊 Results:\n` +
                        `   • Status: ${result.status}\n` +
                        `   • Message: ${result.message}\n` +
                        `   • Emails analyzed: ${result.analyzed_count}\n\n` +
                        `📈 Stakeholder Breakdown:\n` +
                        `   • Warehouse discrepancies: ${result.stakeholder_breakdown?.warehouse_discrepancies || 0}\n` +
                        `   • Supplier responses: ${result.stakeholder_breakdown?.supplier_responses || 0}\n` +
                        `   • Logistics updates: ${result.stakeholder_breakdown?.logistics_updates || 0}\n\n` +
                        `📁 Files Created:\n` +
                        `   • discrepancy.json: ${result.files_created?.['discrepancy.json'] ? '✅' : '❌'}\n` +
                        `   • supplier_response.json: ${result.files_created?.['supplier_response.json'] ? '✅' : '❌'}\n` +
                        `   • logistics.json: ${result.files_created?.['logistics.json'] ? '✅' : '❌'}\n\n` +
                        `📊 Total cockpit analyses: ${result.total_cockpit_analyses}`
                    );
                } else {
                    const error = await response.text();
                    showResult('analyze-result', `❌ API call failed (${response.status}): ${error}`, false);
                }
            } catch (error) {
                showResult('analyze-result', `❌ Network error: ${error.message}`, false);
            }
        }

        async function testFullWorkflow() {
            showLoading('workflow-result', 'Running full SC Copilot KI workflow...');
            
            const steps = [
                { name: 'Step 1: Check Critical Materials', endpoint: 'check-critical-materials' },
                { name: 'Step 2: Compose and Send Emails', endpoint: 'compose-and-send-emails' },
                { name: 'Step 3a: Detect Stakeholder Emails', endpoint: 'detect-stakeholder-emails' },
                { name: 'Step 3b: Analyze Emails with AI', endpoint: 'analyze-emails-with-ai' },
                { name: 'Step 4: Update Cockpit Stats', endpoint: 'cockpit-data' }
            ];

            let results = '🔄 SC Copilot KI Modal Workflow Test\n';
            results += '=' * 50 + '\n\n';

            for (const step of steps) {
                try {
                    results += `🔄 ${step.name}...\n`;
                    
                    const response = await fetch(`${API_BASE}/${step.endpoint}/`, {
                        method: step.endpoint === 'cockpit-data' ? 'GET' : 'POST',
                        headers: { 'Content-Type': 'application/json' }
                    });

                    if (response.ok) {
                        const result = await response.json();
                        results += `✅ ${step.name} completed successfully\n`;
                        
                        if (step.endpoint === 'analyze-emails-with-ai') {
                            results += `   📧 Analyzed ${result.analyzed_count || 0} emails\n`;
                            const breakdown = result.stakeholder_breakdown || {};
                            for (const [stakeholder, count] of Object.entries(breakdown)) {
                                results += `   • ${stakeholder}: ${count}\n`;
                            }
                        }
                    } else {
                        results += `❌ ${step.name} failed (${response.status})\n`;
                    }
                    results += '\n';
                } catch (error) {
                    results += `❌ ${step.name} error: ${error.message}\n\n`;
                }
            }

            showResult('workflow-result', results);
        }

        async function viewGeneratedFiles() {
            showLoading('files-result', 'Checking generated files...');
            
            const files = [
                'discrepancy.json',
                'supplier_response.json', 
                'logistics.json',
                'cockpit.json'
            ];

            let results = '📁 Generated Files Status\n';
            results += '=' * 30 + '\n\n';

            for (const filename of files) {
                try {
                    // Note: In a real implementation, you'd need an endpoint to check file existence
                    // For demo purposes, we'll show what the files would contain
                    results += `📄 ${filename}\n`;
                    
                    if (filename === 'discrepancy.json') {
                        results += '   Contains warehouse inventory discrepancy data\n';
                        results += '   • Material discrepancies with quantities\n';
                        results += '   • Inventory accuracy percentages\n';
                        results += '   • Responsible persons and timestamps\n';
                    } else if (filename === 'supplier_response.json') {
                        results += '   Contains supplier dispatch and production data\n';
                        results += '   • Material dispatch statuses\n';
                        results += '   • Tracking numbers and delivery dates\n';
                        results += '   • Production schedule updates\n';
                    } else if (filename === 'logistics.json') {
                        results += '   Contains logistics and receiving data\n';
                        results += '   • Unloading statuses and dock information\n';
                        results += '   • Material processing queues\n';
                        results += '   • Operational efficiency metrics\n';
                    } else if (filename === 'cockpit.json') {
                        results += '   Contains dashboard and analysis summary\n';
                        results += '   • Email analysis results\n';
                        results += '   • Dashboard statistics\n';
                        results += '   • Confidence scores and timestamps\n';
                    }
                    results += '\n';
                } catch (error) {
                    results += `❌ Error checking ${filename}: ${error.message}\n\n`;
                }
            }

            showResult('files-result', results);
        }

        // Auto-test on page load (optional)
        window.addEventListener('load', () => {
            console.log('🧪 Test page loaded - ready to test /analyze-emails-with-ai/ endpoint');
        });
    </script>
</body>
</html>
