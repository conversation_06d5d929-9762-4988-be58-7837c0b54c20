// Cockpit Dashboard JavaScript Functionality

// Global variables for dashboard
let performanceChart = null;
let coverageChart = null;
let dashboardInterval = null;
let csvData = []; // Store CSV data for charts

// Initialize cockpit dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeCockpit();
});

function initializeCockpit() {
    console.log('Initializing cockpit dashboard...');
    
    // Initialize real-time clock
    updateCurrentTime();
    setInterval(updateCurrentTime, 1000);
    
    // Initialize charts
    initializeCharts();
    
    // Start real-time updates
    startDashboardUpdates();
    
    // Load system information
    loadSystemInfo();
      // Load CSV data
    loadCSVData();
    
    // Initialize search functionality
    initializeTableSearch();
    
    console.log('Cockpit dashboard initialized');
}

function updateCurrentTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString();
    const dateString = now.toLocaleDateString();
    
    const timeElement = document.getElementById('current-time');
    if (timeElement) {
        timeElement.innerHTML = `<div>${timeString}</div><div class="text-xs text-gray-400">${dateString}</div>`;
    }
}

function initializeCharts() {    // Performance Chart - Material Status Overview
    const performanceCtx = document.getElementById('performanceChart');
    if (performanceCtx) {
        performanceChart = new Chart(performanceCtx, {
            type: 'bar',
            data: {
                labels: ['Start', 'Actual', 'Forecast'],
                datasets: [
                    {
                        label: '< 1 AT',
                        data: [2, 4, 1], // Initial placeholder data
                        backgroundColor: 'rgb(239, 68, 68)',
                        // borderColor: 'rgb(239, 68, 68)',
                        borderWidth: 1
                    },
                    {
                        label: '1-3 AT',
                        data: [3, 1, 0], // Initial placeholder data
                        backgroundColor: 'rgb(233, 245, 11)',
                        // borderColor: 'rgb(245, 158, 11)',
                        borderWidth: 1
                    },
                    {
                        label: '3-5 AT',
                        data: [2, 2, 6], // Initial placeholder data
                        backgroundColor: 'rgb(34, 197, 94)',
                        // borderColor: 'rgb(34, 197, 94)',
                        borderWidth: 1
                    }
                ]
            },
            plugins: [ChartDataLabels],
            options: {
                indexAxis: 'y', // This makes the chart horizontal
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: '#ffffff',
                            padding: 15,
                            usePointStyle: true
                        }
                    },
                    datalabels: {
                        anchor: 'center',
                        align: 'center',
                        color: '#ffffff',
                        font: {
                            weight: 'bold',
                            size: 12
                        },
                        formatter: function(value, context) {
                            return value > 0 ? value : '';
                        }
                    }
                },
                scales: {
                    x: {
                        stacked: true,
                        ticks: {
                            color: '#9ca3af',
                            stepSize: 1
                        },
                        grid: {
                            color: '#374151'
                        },
                        beginAtZero: true
                    },
                    y: {
                        stacked: true,
                        ticks: {
                            color: '#9ca3af'
                        },
                        grid: {
                            color: '#374151'
                        }
                    }
                }
            }        });
    }    // Coverage Under One Day Chart
    const coverageCtx = document.getElementById('coverageChart');
    if (coverageCtx) {
        coverageChart = new Chart(coverageCtx, {
            type: 'bar',
            data: {
                labels: [], // Will be populated with material codes
                datasets: [
                    {
                        label: 'Start',
                        data: [],
                        backgroundColor: 'rgb(59, 130, 246)', // Blue
                        // borderColor: 'rgb(59, 130, 246)',
                        borderWidth: 1
                    },
                    {
                        label: 'Current',
                        data: [],
                        backgroundColor: 'rgb(251, 191, 36)', // Yellow/Orange
                        // borderColor: 'rgb(251, 191, 36)',
                        borderWidth: 1
                    },
                    {
                        label: 'Prognose',
                        data: [],
                        backgroundColor: 'rgb(34, 197, 94)', // Green
                        // borderColor: 'rgb(34, 197, 94)',
                        borderWidth: 1
                    }
                ]
            },
            plugins: [ChartDataLabels],
            options: {
                indexAxis: 'y', // This makes the chart horizontal
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            color: '#ffffff',
                            padding: 15,
                            usePointStyle: true
                        }
                    },
                    datalabels: {
                        anchor: 'center',
                        align: 'center',
                        color: '#ffffff',
                        font: {
                            weight: 'bold',
                            size: 10
                        },
                        formatter: function(value, context) {
                            return value > 0 ? value.toFixed(2) : '';
                        }
                    }
                },                scales: {
                    x: {
                        ticks: {
                            color: '#9ca3af',
                            maxRotation: 45
                        },
                        grid: {
                            color: '#374151'
                        },
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Coverage Days',
                            color: '#ffffff'
                        }
                    },
                    y: {
                        ticks: {
                            color: '#9ca3af'
                        },
                        grid: {
                            color: '#374151'
                        },
                        title: {
                            display: true,
                            text: 'Material',
                            color: '#ffffff'
                        }
                    }
                }
            }
        });
    }    // Status Distribution chart has been removed
}

function generateTimeLabels(count) {
    const labels = [];
    const now = new Date();
    
    for (let i = count - 1; i >= 0; i--) {
        const time = new Date(now.getTime() - (i * 5 * 60 * 1000)); // 5-minute intervals
        labels.push(time.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }));
    }
    
    return labels;
}

function generateRandomData(count, min, max) {
    const data = [];
    for (let i = 0; i < count; i++) {
        data.push(Math.floor(Math.random() * (max - min + 1)) + min);
    }
    return data;
}

function startDashboardUpdates() {
    // Update dashboard metrics every 5 seconds
    dashboardInterval = setInterval(() => {
        updateMetrics();
        updateCharts();
    }, 5000);
}

function updateMetrics() {
    // Simulate real-time metric updates
    const cpuUsage = Math.floor(Math.random() * 40) + 30; // 30-70%
    const memoryUsage = Math.floor(Math.random() * 30) + 50; // 50-80%
    const activeUsers = Math.floor(Math.random() * 200) + 1200; // 1200-1400
    
    // Update CPU usage
    const cpuElement = document.getElementById('cpu-usage');
    const cpuProgressBar = cpuElement?.parentElement.querySelector('.progress-bar');
    if (cpuElement) {
        cpuElement.textContent = `${cpuUsage}%`;
        if (cpuProgressBar) {
            cpuProgressBar.style.width = `${cpuUsage}%`;
        }
    }
    
    // Update memory usage
    const memoryElement = document.getElementById('memory-usage');
    const memoryProgressBar = memoryElement?.parentElement.querySelector('.progress-bar');
    if (memoryElement) {
        memoryElement.textContent = `${memoryUsage}%`;
        if (memoryProgressBar) {
            memoryProgressBar.style.width = `${memoryUsage}%`;
        }
    }
    
    // Update active users
    const usersElement = document.getElementById('active-users');
    if (usersElement) {
        usersElement.textContent = activeUsers.toLocaleString();
    }
    
    // Update uptime
    updateUptime();
}

function updateCharts() {
    // Performance chart and API chart now use CSV data, so we don't need to update them here
    // Both charts are updated when CSV data is loaded
}

function loadSystemInfo() {
    // Update system information (in a real app, this would come from APIs)
    const osElement = document.getElementById('os-info');
    const nodeElement = document.getElementById('node-version');
    const pythonElement = document.getElementById('python-version');
    
    if (osElement) {
        osElement.textContent = navigator.platform || 'Windows 11';
    }
    
    // In a real Electron app, you could get actual versions
    if (nodeElement) {
        nodeElement.textContent = process?.versions?.node || 'v18.17.0';
    }
    
    if (pythonElement) {
        // This would typically come from your Python API
        checkPythonVersion();
    }
}

function checkPythonVersion() {
    // Try to get Python version from the API
    const base_url = 'http://127.0.0.1:7777/';
    
    fetch(base_url + 'system-info/')
        .then(response => response.json())
        .then(data => {
            const pythonElement = document.getElementById('python-version');
            if (pythonElement && data.python_version) {
                pythonElement.textContent = data.python_version;
            }
        })
        .catch(error => {
            console.log('Could not fetch Python version:', error);
            // Keep default version if API is not available
        });
}

function updateUptime() {
    // Simulate uptime calculation
    const startTime = localStorage.getItem('cockpit-start-time');
    const currentTime = Date.now();
    
    let uptimeStart;
    if (startTime) {
        uptimeStart = parseInt(startTime);
    } else {
        uptimeStart = currentTime;
        localStorage.setItem('cockpit-start-time', uptimeStart.toString());
    }
    
    const uptimeMs = currentTime - uptimeStart;
    const hours = Math.floor(uptimeMs / (1000 * 60 * 60));
    const minutes = Math.floor((uptimeMs % (1000 * 60 * 60)) / (1000 * 60));
    
    const uptimeElement = document.getElementById('uptime');
    if (uptimeElement) {
        uptimeElement.textContent = `${hours}h ${minutes}m`;
    }
}

// Navigation functions
function openCockpit() {
    // Already on cockpit page, just refresh
    window.location.reload();
}

// Cleanup function
function cleanup() {
    if (dashboardInterval) {
        clearInterval(dashboardInterval);
    }
    
    if (performanceChart) {
        performanceChart.destroy();
    }
      // Status Distribution chart has been removed
    
    if (coverageChart) {
        coverageChart.destroy();
    }
}

// CSV Data Loading Functionality
async function loadCSVData() {
    const loadingElement = document.getElementById('csvTableLoading');
    const tableElement = document.getElementById('csvDataTable');
    
    // Try multiple possible paths for the CSV file
    const possiblePaths = [
        '../../data.csv',           // From public/src/ to root
        '../../../data.csv',        // Alternative path
        './data.csv',               // In case it's in same directory
        '/data.csv',                // Absolute from domain root
        'data.csv'                  // Current directory
    ];
    
    let csvText = null;
    let successPath = null;
    
    for (const path of possiblePaths) {
        try {
            console.log(`Attempting to load CSV from: ${path}`);
            
            const response = await fetch(path);
            
            if (response.ok) {
                csvText = await response.text();
                successPath = path;
                console.log(`✓ Successfully loaded CSV from: ${path}`);
                break;
            } else {
                console.log(`✗ Failed to load from ${path}: ${response.status}`);
            }
        } catch (error) {
            console.log(`✗ Error loading from ${path}:`, error.message);
        }
    }
    
    if (!csvText) {
        console.error('Failed to load CSV from any path');
        showCSVError();
        
        // Hide loading
        if (loadingElement) loadingElement.style.display = 'none';
        if (tableElement) tableElement.classList.remove('hidden');
        return;
    }
    
    try {
        console.log('CSV loaded, parsing data...');
        
        // Parse CSV data with better handling of multi-line entries
        const data = parseCSV(csvText);
        
        console.log(`Parsed ${data.length} rows from CSV`);        populateDataTable(data);        
        
        // Store CSV data globally for charts
        csvData = data;
        
        // Update performance chart with real data
        updatePerformanceChartWithCSVData();
        
        // Update coverage chart with materials under one day
        updateCoverageChartWithCSVData();        
          // Status Distribution chart has been removed
        
        // Hide loading and show table
        if (loadingElement) loadingElement.style.display = 'none';
        if (tableElement) tableElement.classList.remove('hidden');
        
    } catch (error) {
        console.error('Error parsing CSV data:', error);
        showCSVError();
        
        // Hide loading
        if (loadingElement) loadingElement.style.display = 'none';
        if (tableElement) tableElement.classList.remove('hidden');
    }
}

function parseCSV(csvText) {
    const lines = csvText.split('\n');
    const result = [];
    let i = 0;
    
    // Skip comment lines and find header
    while (i < lines.length && (lines[i].trim() === '' || lines[i].startsWith('//'))) {
        i++;
    }
    
    // Skip header row
    i++;
    
    // Parse data rows
    while (i < lines.length) {
        if (lines[i].trim() === '' || lines[i].startsWith('//')) {
            i++;
            continue;
        }
        
        const row = parseCSVRow(lines, i);
        if (row.values && row.values.length > 0) {
            result.push(row.values);
        }
        i = row.nextIndex;
    }
    
    return result;
}

function parseCSVRow(lines, startIndex) {
    const result = [];
    let current = '';
    let inQuotes = false;
    let lineIndex = startIndex;
    
    while (lineIndex < lines.length) {
        const line = lines[lineIndex];
        
        for (let i = 0; i < line.length; i++) {
            const char = line[i];
            
            if (char === '"') {
                inQuotes = !inQuotes;
            } else if (char === ',' && !inQuotes) {
                result.push(current.trim().replace(/"/g, ''));
                current = '';
            } else {
                current += char;
            }
        }
        
        // If we're still in quotes, add a newline and continue to next line
        if (inQuotes && lineIndex < lines.length - 1) {
            current += '\n';
            lineIndex++;
        } else {
            // End of this row
            break;
        }
    }
    
    // Add the last field
    result.push(current.trim().replace(/"/g, ''));
    
    return {
        values: result,
        nextIndex: lineIndex + 1
    };
}

function populateDataTable(data) {
    const tableBody = document.getElementById('csvTableBody');
    if (!tableBody) return;
    
    tableBody.innerHTML = '';
    
    console.log(`Populating table with ${data.length} rows`);
    
    data.forEach((row, index) => {
        console.log(`Row ${index + 1}: ${row.length} columns`);
        
        // Be more lenient - just check if we have at least the basic columns we need
        if (row.length >= 13) { // We need at least columns up to Avise (index 12)
            const tr = document.createElement('tr');
            
            // Get values with fallbacks
            const material = row[1] || '';
            const materialText = row[2] || '';
            const stock = row[5] || '';
            const avise = row[12] || '';
            const status = row[21] || ''; // Status might not always be present
            
            // Calculate new columns based on Stock value
            const stockValue = parseFloat(stock) || 0;
            const startValue = (stockValue / 100).toFixed(2);
            const actualValue = (stockValue / 100).toFixed(2);
            const forecastValue = "0.00";
              tr.innerHTML = `
                <td class="text-center"><span class="material-code">${material}</span></td>
                <td class="text-center text-gray-300 max-w-xs">
                    <div title="${materialText}">${materialText}</div>
                </td>
                <td class="text-center text-white">${stock}</td>
                <td class="text-center text-white">${startValue}</td>
                <td class="text-center text-white">${actualValue}</td>
                <td class="text-center text-white">${forecastValue}</td>
                <td class="text-center text-white">${avise}</td>
                <td class="text-center">
                    <div class="status-dot ${getStatusDotClass(status)}" title="${status || 'Unknown'}"></div>
                </td>
            `;
            tableBody.appendChild(tr);
        } else {
            console.log(`Skipping row ${index + 1} - not enough columns: ${row.length}`);
        }
    });
    
    console.log(`Table populated with ${tableBody.children.length} rows`);
}

function getStatusDotClass(status) {
    if (!status) return 'status-dot-gray';
    
    const statusLower = status.toLowerCase();
    if (statusLower.includes('red')) return 'status-dot-red';
    if (statusLower.includes('yellow')) return 'status-dot-yellow';
    if (statusLower.includes('green')) return 'status-dot-green';
    return 'status-dot-gray';
}

function showCSVError() {
    const tableBody = document.getElementById('csvTableBody');
    if (tableBody) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-8">
                    <div class="text-red-400">
                        <i class="fas fa-exclamation-triangle text-2xl mb-2"></i>
                        <div>Error loading data. Please check if data.csv file exists.</div>
                    </div>
                </td>
            </tr>
        `;
    }
}

// Function to update performance chart with CSV data
function updatePerformanceChartWithCSVData() {
    if (!performanceChart || !csvData.length) return;
    
    // Initialize counters for each category
    const startCounts = { red: 0, yellow: 0, green: 0 };
    const actualCounts = { red: 0, yellow: 0, green: 0 };
    const forecastCounts = { red: 0, yellow: 0, green: 0 };
    
    // Process CSV data
    csvData.forEach(row => {
        if (row.length >= 13) {
            const stock = parseFloat(row[5]) || 0;
            const startValue = stock / 100;
            const actualValue = stock / 100;
            const forecastValue = 0; // Always 0 as per current logic
            
            // Categorize based on value ranges (similar to AT days)
            // < 1 AT = red, 1-3 AT = yellow, 3-5 AT = green
            
            // Start column categorization
            if (startValue < 1) startCounts.red++;
            else if (startValue <= 3) startCounts.yellow++;
            else startCounts.green++;
            
            // Actual column categorization  
            if (actualValue < 1) actualCounts.red++;
            else if (actualValue <= 3) actualCounts.yellow++;
            else actualCounts.green++;
            
            // Forecast column categorization
            if (forecastValue < 1) forecastCounts.red++;
            else if (forecastValue <= 3) forecastCounts.yellow++;
            else forecastCounts.green++;
        }
    });
    
    // Update chart data
    performanceChart.data.datasets[0].data = [startCounts.red, actualCounts.red, forecastCounts.red];
    performanceChart.data.datasets[1].data = [startCounts.yellow, actualCounts.yellow, forecastCounts.yellow];
    performanceChart.data.datasets[2].data = [startCounts.green, actualCounts.green, forecastCounts.green];
    
    // Use solid colors (no gradients needed)
    performanceChart.data.datasets[0].backgroundColor = 'rgba(255, 33, 33, 0.79)';
    performanceChart.data.datasets[1].backgroundColor = 'rgba(255, 234, 0, 0.88)';
    performanceChart.data.datasets[2].backgroundColor = 'rgba(0, 202, 61, 0.98)';
    
    performanceChart.update();
}

// Function to update API chart with CSV status data - removed
function updateApiChartWithCSVData() {
    // Status Distribution chart functionality has been removed
    return;
}

// Function to update coverage chart with materials where start < 1
function updateCoverageChartWithCSVData() {
    if (!coverageChart || !csvData.length) return;
    
    const materialsUnderOneDay = [];
    const labels = [];
    const startData = [];
    const currentData = [];
    const prognoseData = [];
    
    // Process CSV data to find materials with start values < 1
    csvData.forEach(row => {
        if (row.length >= 13) {
            const material = row[1] || '';
            const stock = parseFloat(row[5]) || 0;
            const startValue = stock / 100;
            
            // Only include materials where start value is less than 1
            if (startValue < 1 && material) {
                const actualValue = startValue; // Same as start for now
                const forecastValue = 0; // Default to 0 for forecast
                
                // Limit to reasonable number of items for chart readability
                if (materialsUnderOneDay.length < 10) {
                    materialsUnderOneDay.push({
                        material: material,
                        start: startValue,
                        current: actualValue,
                        prognose: forecastValue
                    });
                    
                    labels.push(material);
                    startData.push(startValue);
                    currentData.push(actualValue);
                    prognoseData.push(forecastValue);
                }
            }
        }
    });
    
    // Update chart data
    coverageChart.data.labels = labels;
    coverageChart.data.datasets[0].data = startData;
    coverageChart.data.datasets[1].data = currentData;
    coverageChart.data.datasets[2].data = prognoseData;
      // Update chart
    coverageChart.update();
}



// Table Search Functionality
function initializeTableSearch() {
    const searchInput = document.getElementById('tableSearchInput');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(filterTable, 300));
    }
}

function filterTable() {
    const searchInput = document.getElementById('tableSearchInput');
    const table = document.getElementById('csvDataTable');
    const tableBody = document.getElementById('csvTableBody');
    
    if (!searchInput || !table || !tableBody) return;
    
    const searchTerm = searchInput.value.toLowerCase().trim();
    const rows = tableBody.getElementsByTagName('tr');
    let visibleCount = 0;
    
    // If search term is empty, show all rows and remove highlights
    if (searchTerm === '') {
        for (let row of rows) {
            row.style.display = '';
            removeHighlights(row);
            visibleCount++;
        }
    } else {
        // Filter rows based on search term
        for (let row of rows) {
            const cells = row.getElementsByTagName('td');
            let shouldShow = false;
            
            // Remove previous highlights
            removeHighlights(row);
            
            // Search through all text content in the row
            for (let cell of cells) {
                const cellText = cell.textContent.toLowerCase();
                if (cellText.includes(searchTerm)) {
                    shouldShow = true;
                    // Highlight the search term
                    highlightSearchTerm(cell, searchTerm);
                }
            }
            
            if (shouldShow) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        }
    }
    
    // Update search results indicator
    updateSearchResults(visibleCount, rows.length);
}

function highlightSearchTerm(cell, searchTerm) {
    // Skip cells that contain HTML elements like status dots
    if (cell.querySelector('.status-dot') || cell.querySelector('.material-code')) {
        return;
    }
    
    const originalText = cell.textContent;
    const lowerText = originalText.toLowerCase();
    const searchIndex = lowerText.indexOf(searchTerm.toLowerCase());
    
    if (searchIndex !== -1) {
        const beforeMatch = originalText.substring(0, searchIndex);
        const match = originalText.substring(searchIndex, searchIndex + searchTerm.length);
        const afterMatch = originalText.substring(searchIndex + searchTerm.length);
        
        cell.innerHTML = `${beforeMatch}<span class="search-highlight">${match}</span>${afterMatch}`;
    }
}

function removeHighlights(row) {
    const highlights = row.querySelectorAll('.search-highlight');
    highlights.forEach(highlight => {
        const parent = highlight.parentNode;
        parent.replaceChild(document.createTextNode(highlight.textContent), highlight);
        parent.normalize();
    });
}

function updateSearchResults(visibleCount, totalCount) {
    const searchInput = document.getElementById('tableSearchInput');
    if (!searchInput) return;
    
    // Update placeholder text to show results
    if (searchInput.value.trim() !== '') {
        searchInput.placeholder = `${visibleCount} of ${totalCount} results`;
    } else {
        searchInput.placeholder = 'Search materials...';
    }
}

// Debounce function to limit search frequency
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Clean up when leaving the page
window.addEventListener('beforeunload', cleanup);

// Export functions for global access
window.openCockpit = openCockpit;
window.updateMetrics = updateMetrics;
window.cleanup = cleanup;
window.loadCSVData = loadCSVData;
window.filterTable = filterTable;
window.updatePerformanceChartWithCSVData = updatePerformanceChartWithCSVData;
window.updateCoverageChartWithCSVData = updateCoverageChartWithCSVData;
window.updateStatusBarCharts = updateStatusBarCharts;
window.updateCoverageBarCharts = updateCoverageBarCharts;
