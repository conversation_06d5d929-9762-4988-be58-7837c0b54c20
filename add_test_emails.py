#!/usr/bin/env python3
"""
Script to add test emails to incoming_emails.json for testing the analysis functionality
"""

import json
import os
from datetime import datetime, timedelta

def create_test_emails():
    """Create test emails for different stakeholder types"""
    
    # Sample test emails for different stakeholders
    test_emails = [
        {
            "id": f"{datetime.now().timestamp()}_test_1",
            "subject": "RE: Physical Inventory Check - Material M12345",
            "sender": "Warehouse Team",
            "sender_email": "<EMAIL>",
            "body": """Dear Supply Chain Team,

We have completed the physical inventory check for Material M12345 as requested.

Physical Count Results:
- Material Number: M12345
- System Quantity: 85 units
- Physical Count: 78 units
- Discrepancy: -7 units (shortage)
- Location: Warehouse Section A-15
- Condition: Good condition, no damage found

The discrepancy appears to be due to unreported usage from last week's production run. We have updated the system records and will implement better tracking procedures.

Next Actions:
- Updated system stock to reflect actual count
- Investigating the missing 7 units
- Will provide daily stock reports for critical materials

Best regards,
Warehouse Operations Team""",
            "received_time": (datetime.now() - timedelta(hours=2)).isoformat(),
            "stakeholder_type": "warehouse",
            "processed": False,
            "detected_timestamp": datetime.now().isoformat()
        },
        {
            "id": f"{datetime.now().timestamp()}_test_2",
            "subject": "RE: Material Dispatch Status - Part M67890",
            "sender": "Supplier Production",
            "sender_email": "<EMAIL>",
            "body": """Dear Procurement Team,

Thank you for your urgent request regarding Material M67890. Here is the current status:

Production Status:
- Material Number: M67890
- Current Production: 150 units in progress
- Finished Goods: 200 units ready for dispatch
- Next Production Run: Tomorrow (500 units planned)

Dispatch Information:
- Dispatched Today: 100 units via Express Logistics
- Tracking Number: EXP123456789
- Expected Delivery: Tomorrow 10:00 AM
- Remaining 100 units will be dispatched tomorrow afternoon

Transit Status:
- Current Location: Distribution Center
- Status: In Transit
- No delays expected

We are prioritizing this material due to your critical stock situation.

Best regards,
Supplier Production Team""",
            "received_time": (datetime.now() - timedelta(hours=1)).isoformat(),
            "stakeholder_type": "supplier",
            "processed": False,
            "detected_timestamp": datetime.now().isoformat()
        },
        {
            "id": f"{datetime.now().timestamp()}_test_3",
            "subject": "RE: Unloading Status Update - Critical Materials",
            "sender": "Logistics Operations",
            "sender_email": "<EMAIL>",
            "body": """Dear Operations Control,

Current status of receiving dock operations for critical materials:

Receiving Dock Status:
- Trucks in Queue: 3 trucks waiting
- Currently Unloading: Truck #EXP123456789 (Material M67890)
- Estimated Completion: 30 minutes

Material Processing Status:
- Material M67890: Currently being unloaded (100 units)
- Quality Check: In progress
- Expected Warehouse Transfer: Within 1 hour
- Priority Processing: Activated for critical materials

Operational Updates:
- Added extra personnel for expedited processing
- Quality inspection fast-tracked for critical materials
- Direct transfer to warehouse upon completion

No delays expected. Will provide update upon completion.

Best regards,
Logistics Operations Team""",
            "received_time": (datetime.now() - timedelta(minutes=30)).isoformat(),
            "stakeholder_type": "logistics",
            "processed": False,
            "detected_timestamp": datetime.now().isoformat()
        }
    ]
    
    return test_emails

def add_test_emails_to_file():
    """Add test emails to incoming_emails.json"""
    
    incoming_emails_path = "incoming_emails.json"
    
    # Load existing emails or create new structure
    if os.path.exists(incoming_emails_path):
        try:
            with open(incoming_emails_path, 'r', encoding='utf-8') as f:
                emails_data = json.load(f)
            existing_emails = emails_data.get('emails', [])
            print(f"📧 Loaded existing file with {len(existing_emails)} emails")
        except Exception as e:
            print(f"❌ Error loading existing file: {str(e)}")
            existing_emails = []
    else:
        existing_emails = []
        print("📧 Creating new incoming_emails.json file")
    
    # Create test emails
    test_emails = create_test_emails()
    
    # Check for duplicates (avoid adding same test emails multiple times)
    existing_subjects = [email.get('subject', '') for email in existing_emails]
    new_emails = []
    
    for test_email in test_emails:
        if test_email['subject'] not in existing_subjects:
            new_emails.append(test_email)
        else:
            print(f"⚠️  Skipping duplicate email: {test_email['subject']}")
    
    if not new_emails:
        print("ℹ️  No new test emails to add (all already exist)")
        return
    
    # Combine existing and new emails
    all_emails = existing_emails + new_emails
    
    # Create the final data structure
    emails_data = {
        "timestamp": datetime.now().isoformat(),
        "total_emails": len(all_emails),
        "new_emails_detected": len(new_emails),
        "emails": all_emails
    }
    
    # Save to file
    try:
        with open(incoming_emails_path, 'w', encoding='utf-8') as f:
            json.dump(emails_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Successfully added {len(new_emails)} test emails")
        print(f"📊 Total emails in file: {len(all_emails)}")
        print(f"📁 File saved: {os.path.abspath(incoming_emails_path)}")
        
        # Show summary of added emails
        print("\n📧 Added test emails:")
        for email in new_emails:
            print(f"   - {email['stakeholder_type']}: {email['subject']}")
        
    except Exception as e:
        print(f"❌ Error saving file: {str(e)}")

def main():
    """Main function"""
    print("🧪 Adding Test Emails for SC Copilot KI Analysis")
    print("=" * 50)
    
    add_test_emails_to_file()
    
    print("\n📝 Next Steps:")
    print("1. Run the test_email_workflow.py script to test the complete workflow")
    print("2. Or test the analysis endpoint directly: POST /analyze-emails-with-ai/")
    print("3. Check the generated analysis files (discrepancy.json, supplier_response.json, logistics.json)")

if __name__ == "__main__":
    main()
