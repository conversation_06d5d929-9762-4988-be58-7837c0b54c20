// This file is required by the index.html file and will
// be executed in the renderer process for that window.
// No Node.js APIs are available in this process because
// `nodeIntegration` is turned off. Use `preload.js` to
// selectively enable features needed in the rendering
// process.

// Import Flyon UI - DISABLED (library not installed)
// import 'flyonui/dist/style.css';
// import { SoftButton } from 'flyonui';

// Replace UI elements with Flyon UI components after DOM is loaded - DISABLED
window.addEventListener('DOMContentLoaded', () => {
  console.log('Renderer.js loaded - Flyon UI integration disabled');

  // Flyon UI integration is disabled since the library is not installed
  // The application will use the default styling instead

  /*
  // Remove Tabler CSS if present
  const tablerLink = document.querySelector('link[href*="tabler.min.css"]');
  if (tablerLink) tablerLink.remove();
  // Add Flyon UI CSS if not present
  if (!document.querySelector('link[href*="flyonui/dist/style.css"]')) {
    const flyonLink = document.createElement('link');
    flyonLink.rel = 'stylesheet';
    flyonLink.href = '../node_modules/flyonui/dist/style.css';
    document.head.appendChild(flyonLink);
  }

  // Replace all .btn with Flyon UI SoftButton
  document.querySelectorAll('.btn').forEach(btn => {
    // Create a new SoftButton element
    const softBtn = document.createElement('button');
    softBtn.className = 'flyon-soft-btn';
    softBtn.innerHTML = btn.innerHTML;
    // Copy over id and type
    if (btn.id) softBtn.id = btn.id;
    if (btn.type) softBtn.type = btn.type;
    // Copy over onclick handler
    if (btn.onclick) softBtn.onclick = btn.onclick;
    // Copy over any data attributes
    Array.from(btn.attributes).forEach(attr => {
      if (attr.name.startsWith('data-')) {
        softBtn.setAttribute(attr.name, attr.value);
      }
    });
    // Replace in DOM
    btn.parentNode.replaceChild(softBtn, btn);
  });

  // Replace all .form-control with Flyon UI input
  document.querySelectorAll('.form-control').forEach(input => {
    input.classList.remove('form-control', 'mt-2', 'mt-4');
    input.classList.add('flyon-input');
  });

  // Replace all .card with Flyon UI card
  document.querySelectorAll('.card').forEach(card => {
    card.classList.remove('card');
    card.classList.add('flyon-card');
  });

  // Replace all .card-title with Flyon UI card title
  document.querySelectorAll('.card-title').forEach(title => {
    title.classList.remove('card-title');
    title.classList.add('flyon-card-title');
  });

  // Replace all .card-body with Flyon UI card body
  document.querySelectorAll('.card-body').forEach(body => {
    body.classList.remove('card-body');
    body.classList.add('flyon-card-body');
  });
  */
});