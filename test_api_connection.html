<!DOCTYPE html>
<html>
<head>
    <title>API Connection Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>SC Copilot KI API Connection Test</h1>
    
    <div>
        <button onclick="testApiConnection()">Test API Connection</button>
        <button onclick="testStep1()">Test Step 1: Check Critical Materials</button>
        <button onclick="testStep2()">Test Step 2: Compose Emails</button>
        <button onclick="testStep3()">Test Step 3: Analyze Emails</button>
        <button onclick="testStep4()">Test Step 4: Update Cockpit</button>
    </div>
    
    <div id="results"></div>

    <script>
        const API_BASE_URL = 'http://127.0.0.1:7777';
        
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        async function testApiConnection() {
            addResult('Testing basic API connection...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/docs`);
                if (response.ok) {
                    addResult('✅ API server is reachable and responding', 'success');
                } else {
                    addResult(`❌ API server responded with status: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Failed to connect to API server: ${error.message}`, 'error');
            }
        }
        
        async function testStep1() {
            addResult('Testing Step 1: Check Critical Materials...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/check-critical-materials/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Step 1 successful: Found ${data.critical_materials_count} critical materials`, 'success');
                    addResult(`Details: ${data.message}`, 'info');
                } else {
                    const errorText = await response.text();
                    addResult(`❌ Step 1 failed with status ${response.status}: ${errorText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Step 1 failed: ${error.message}`, 'error');
            }
        }
        
        async function testStep2() {
            addResult('Testing Step 2: Compose and Send Emails...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/compose-and-send-emails/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Step 2 successful: ${data.status}`, 'success');
                    addResult(`Details: Attempted ${data.total_emails_attempted} emails`, 'info');
                } else {
                    const errorText = await response.text();
                    addResult(`❌ Step 2 failed with status ${response.status}: ${errorText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Step 2 failed: ${error.message}`, 'error');
            }
        }
        
        async function testStep3() {
            addResult('Testing Step 3: Analyze Emails with AI...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/analyze-emails-with-ai/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Step 3 successful: ${data.status}`, 'success');
                    addResult(`Details: Analyzed ${data.analyzed_count} emails`, 'info');
                } else {
                    const errorText = await response.text();
                    addResult(`❌ Step 3 failed with status ${response.status}: ${errorText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Step 3 failed: ${error.message}`, 'error');
            }
        }
        
        async function testStep4() {
            addResult('Testing Step 4: Update Cockpit Stats...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/update-cockpit-stats/`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Step 4 successful: ${data.status}`, 'success');
                    addResult(`Details: Updated cockpit with latest data`, 'info');
                } else {
                    const errorText = await response.text();
                    addResult(`❌ Step 4 failed with status ${response.status}: ${errorText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Step 4 failed: ${error.message}`, 'error');
            }
        }
        
        // Auto-test connection on page load
        window.onload = function() {
            addResult('Page loaded. Click buttons to test API endpoints.', 'info');
            testApiConnection();
        };
    </script>
</body>
</html>
