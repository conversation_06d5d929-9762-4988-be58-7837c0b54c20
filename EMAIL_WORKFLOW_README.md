# SC Copilot KI - Email Workflow Fix

## Overview

The 'Automated Email Composition & Sending' component in the SC Copilot KI system has been fixed and enhanced. The workflow now properly:

1. **Reads stakeholder emails** from `email_info.csv`
2. **Stores incoming emails** in `incoming_emails.json`
3. **Analyzes emails** using Azure OpenAI
4. **Categorizes responses** by stakeholder type (warehouse, supplier, logistics)

## Fixed Components

### 1. Email Detection (`detect_and_store_stakeholder_emails`)
- ✅ Reads stakeholder definitions from `email_info.csv`
- ✅ Uses pywin32 to scan Outlook inbox
- ✅ Stores detected emails in `incoming_emails.json`
- ✅ Enhanced error handling and debugging

### 2. Email Analysis (`analyze_emails_with_ai`)
- ✅ Analyzes emails stored in `incoming_emails.json`
- ✅ Uses Azure OpenAI for structured information extraction
- ✅ Creates stakeholder-specific analysis files
- ✅ Supports warehouse, supplier, and logistics email types

### 3. Workflow Integration
- ✅ Updated HTML interface with better status messages
- ✅ Enhanced error handling and logging
- ✅ Improved user feedback during workflow execution

## File Structure

```
sc_op_v4/
├── email_info.csv              # Stakeholder email definitions
├── incoming_emails.json        # Detected emails storage
├── discrepancy.json           # Warehouse analysis results
├── supplier_response.json     # Supplier analysis results
├── logistics.json             # Logistics analysis results
├── engine/
│   └── api.py                 # Main API with email functions
├── public/src/
│   └── index.html             # Updated workflow interface
├── test_email_workflow.py     # Test script for workflow
├── add_test_emails.py         # Script to add test emails
└── setup_environment.py       # Environment setup script
```

## Setup Instructions

### 1. Environment Configuration

Run the setup script to check your environment:
```bash
python setup_environment.py
```

### 2. Azure OpenAI Configuration

Set the following environment variables:
```bash
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_KEY=your_api_key_here
AZURE_OPENAI_DEPLOYMENT=gpt-4
AZURE_OPENAI_API_VERSION=2024-02-01
```

### 3. Email Configuration

Ensure `email_info.csv` contains your stakeholder email addresses:
```csv
stakeholder,email_address,department
warehouse,<EMAIL>,Warehouse Management
supplier,<EMAIL>,Supply Chain
logistics,<EMAIL>,Logistics Department
```

## Testing the Workflow

### 1. Add Test Emails
```bash
python add_test_emails.py
```

### 2. Run Complete Workflow Test
```bash
python test_email_workflow.py
```

### 3. Test Individual Components

**Test email detection:**
```bash
curl -X POST http://127.0.0.1:7777/detect-stakeholder-emails/
```

**Test email analysis:**
```bash
curl -X POST http://127.0.0.1:7777/analyze-emails-with-ai/
```

## Workflow Steps Explained

### Step 1: Critical Material Detection
- Scans `data.csv` for materials with stock ≤ 100
- Stores results in `critical_materials.json`

### Step 2: Email Composition & Sending
- **Reads stakeholder emails from `email_info.csv`**
- Composes custom emails for each stakeholder type
- Sends emails via Outlook integration

### Step 3: Email Detection & Analysis
- **Scans Outlook inbox for emails from defined stakeholders**
- **Stores detected emails in `incoming_emails.json`**
- **Analyzes emails using Azure OpenAI**
- Extracts structured information by stakeholder type

### Step 4: Dashboard Update
- Updates cockpit statistics with analysis results
- Provides real-time insights

## Email Analysis Features

### Warehouse Emails
Extracts:
- Material discrepancies
- Physical count results
- Inventory status
- Location information

### Supplier Emails
Extracts:
- Production quantities
- Dispatch status
- Transit information
- Delivery schedules

### Logistics Emails
Extracts:
- Receiving dock status
- Unloading operations
- Material processing queue
- Transfer schedules

## Troubleshooting

### Common Issues

1. **Azure OpenAI Not Configured**
   - Check environment variables
   - Verify API key and endpoint
   - Test with `setup_environment.py`

2. **Outlook Integration Issues**
   - Ensure Outlook is installed and configured
   - Check if pywin32 is properly installed
   - Verify email account access

3. **No Emails Detected**
   - Check `email_info.csv` format
   - Verify stakeholder email addresses
   - Add test emails with `add_test_emails.py`

4. **File Path Issues**
   - Ensure all files are in the project root
   - Check file permissions
   - Verify CSV file format

### Debug Mode

Enable detailed logging by checking the console output during workflow execution. The system now provides comprehensive debug information for each step.

## API Endpoints

- `POST /detect-stakeholder-emails/` - Detect and store emails
- `POST /analyze-emails-with-ai/` - Analyze stored emails
- `POST /compose-and-send-emails/` - Send emails to stakeholders
- `GET /update-cockpit-stats/` - Update dashboard statistics

## Files Generated

- `incoming_emails.json` - All detected stakeholder emails
- `discrepancy.json` - Warehouse analysis results
- `supplier_response.json` - Supplier analysis results
- `logistics.json` - Logistics analysis results
- `critical_materials.json` - Critical materials data

## Next Steps

1. **Configure Azure OpenAI** with your credentials
2. **Update email_info.csv** with actual stakeholder emails
3. **Test the workflow** using the provided scripts
4. **Run the complete workflow** from the web interface
5. **Monitor the generated analysis files** for results

The email workflow is now fully functional and ready for production use!
