from fastapi import <PERSON><PERSON><PERSON>, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
import api_model
import os
import win32com.client
import pandas as pd
import json
from datetime import datetime
from typing import List, Dict, Any
import asyncio
from openai import AzureOpenAI
from dotenv import load_dotenv
import re

# Load environment variables
load_dotenv()

HOST = "127.0.0.1"
PORT = 7777

app = FastAPI()

# Add CORS middleware to allow frontend requests
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for development
    allow_credentials=True,
    allow_methods=["*"],  # Allow all methods
    allow_headers=["*"],  # Allow all headers
)

DEFAULT_EMAIL_ACCOUNT = "<EMAIL>"

# Azure OpenAI Configuration
AZURE_OPENAI_ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT", "")
AZURE_OPENAI_API_KEY = os.getenv("AZURE_OPENAI_API_KEY", "")
AZURE_OPENAI_DEPLOYMENT = os.getenv("AZURE_OPENAI_DEPLOYMENT", "gpt-4")
AZURE_OPENAI_API_VERSION = os.getenv("AZURE_OPENAI_API_VERSION", "2024-02-01")

# File paths - dynamically resolve based on script location
def get_project_root():
    """Get the project root directory (sc_op_v5)"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # If we're in the engine directory, go up one level
    if os.path.basename(current_dir) == 'engine':
        return os.path.dirname(current_dir)
    # Otherwise, assume we're already in the project root
    return current_dir

PROJECT_ROOT = get_project_root()
DATA_CSV_PATH = os.path.join(PROJECT_ROOT, "data.csv")
EMAIL_INFO_CSV_PATH = os.path.join(PROJECT_ROOT, "email_info.csv")
CRITICAL_MATERIALS_JSON_PATH = os.path.join(PROJECT_ROOT, "critical_materials.json")
INCOMING_EMAILS_JSON_PATH = os.path.join(PROJECT_ROOT, "incoming_emails.json")
COCKPIT_JSON_PATH = os.path.join(PROJECT_ROOT, "cockpit.json")
DISCREPANCY_JSON_PATH = os.path.join(PROJECT_ROOT, "discrepancy.json")
SUPPLIER_RESPONSE_JSON_PATH = os.path.join(PROJECT_ROOT, "supplier_response.json")
LOGISTICS_JSON_PATH = os.path.join(PROJECT_ROOT, "logistics.json")

print(f"[DEBUG] Project root: {PROJECT_ROOT}")
print(f"[DEBUG] Data CSV path: {DATA_CSV_PATH}")
print(f"[DEBUG] File exists: {os.path.exists(DATA_CSV_PATH)}")

def initialize_azure_openai():
    """Initialize Azure OpenAI client"""
    print(f"[DEBUG] Initializing Azure OpenAI client...")
    print(f"[DEBUG] Endpoint configured: {bool(AZURE_OPENAI_ENDPOINT)}")
    print(f"[DEBUG] API Key configured: {bool(AZURE_OPENAI_API_KEY)}")
    print(f"[DEBUG] Deployment: {AZURE_OPENAI_DEPLOYMENT}")
    print(f"[DEBUG] API Version: {AZURE_OPENAI_API_VERSION}")

    if not AZURE_OPENAI_ENDPOINT or not AZURE_OPENAI_API_KEY:
        error_msg = "Azure OpenAI configuration missing. Please set AZURE_OPENAI_ENDPOINT and AZURE_OPENAI_API_KEY environment variables."
        print(f"[DEBUG] Configuration error: {error_msg}")
        raise HTTPException(status_code=500, detail=error_msg)

    try:
        client = AzureOpenAI(
            azure_endpoint=AZURE_OPENAI_ENDPOINT,
            api_key=AZURE_OPENAI_API_KEY,
            api_version=AZURE_OPENAI_API_VERSION,
        )
        print(f"[DEBUG] Azure OpenAI client initialized successfully")
        return client
    except Exception as e:
        print(f"[DEBUG] Error initializing Azure OpenAI client: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to initialize Azure OpenAI client: {str(e)}")

def load_email_info() -> Dict[str, str]:
    """Load email information from CSV"""
    try:
        print(f"[DEBUG] Loading email info from: {EMAIL_INFO_CSV_PATH}")
        print(f"[DEBUG] File exists: {os.path.exists(EMAIL_INFO_CSV_PATH)}")

        df = pd.read_csv(EMAIL_INFO_CSV_PATH)
        email_mapping = dict(zip(df['stakeholder'], df['email_address']))
        print(f"[DEBUG] Loaded email mapping: {email_mapping}")
        return email_mapping
    except FileNotFoundError:
        print(f"[DEBUG] Email info CSV not found, using default mapping")
        default_mapping = {
            'warehouse': '<EMAIL>',
            'supplier': '<EMAIL>',
            'logistics': '<EMAIL>'
        }
        print(f"[DEBUG] Default email mapping: {default_mapping}")
        return default_mapping
    except Exception as e:
        print(f"[DEBUG] Error loading email info: {str(e)}")
        return {
            'warehouse': '<EMAIL>',
            'supplier': '<EMAIL>',
            'logistics': '<EMAIL>'
        }

def check_critical_materials() -> List[api_model.MaterialModel]:
    """Check for materials with stock 100 or less from data.csv"""
    try:
        df = pd.read_csv(DATA_CSV_PATH)
        
        # Filter materials with stock 100 or less
        critical_materials = df[df['Stock'] <= 100].copy()
        
        materials = []
        for _, row in critical_materials.iterrows():
            material = api_model.MaterialModel(
                rule=str(row.get('Rule', '')),
                material=str(row.get('Material', '')),
                material_short_text=str(row.get('Material short text', '')),
                result_description=str(row.get('Result for mock-up with actual range', '')),
                sap_range=float(row.get('SAP range', 0)),
                stock=int(row.get('Stock', 0)),
                status=str(row.get('Status', '')),
                coverage_status=str(row.get('Coverage Status', '')),
                coverage_days=str(row.get('Coverage Days', ''))
            )
            materials.append(material)
        
        return materials
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error reading materials data: {str(e)}")

def compose_email_content_for_material(stakeholder: str, material: api_model.MaterialModel, material_index: int, total_materials: int) -> tuple:
    """Compose email content for a single critical material to a specific stakeholder"""
    
    if stakeholder == "warehouse":
        subject = f"URGENT: Physical Inventory Check Required - Material {material.material} (Stock: {material.stock} units)"
        body = f"""Dear Warehouse Team,

CRITICAL MATERIAL ALERT - Individual Material Check Required

Material Details:
- Material Number: {material.material}
- Description: {material.material_short_text}
- Current System Stock: {material.stock} units
- Status: {material.status}
- Coverage Status: {material.coverage_status}
- Coverage Days: {material.coverage_days}
- Rule: {material.rule}

REQUESTED ACTION: Immediate Physical Inventory Check for This Material

IMMEDIATE ACTIONS REQUIRED:

1. PHYSICAL COUNT VERIFICATION:
   - Conduct immediate physical count for Material {material.material}
   - Verify actual quantity against system stock of {material.stock} units
   - Check all storage locations (primary, overflow, quarantine areas)
   - Document any discrepancies found

2. STOCK CONDITION ASSESSMENT:
   - Inspect material condition (damaged, expired, quality issues)
   - Verify material identification and labeling
   - Check for any misplaced or mislabeled items

3. LOCATION VERIFICATION:
   - Confirm correct storage location for Material {material.material}
   - Update location records if material has been moved
   - Identify if material is in receiving/staging areas

4. URGENT REPORTING:
   - Complete physical inventory count for this specific material
   - Report discrepancies immediately via email
   - Provide updated stock level within 2 HOURS
   - Include photos for any damaged or questionable material

DEADLINE: Please complete physical inventory check and report results within 2 HOURS for this critical material.

Priority Level: HIGH - This is {material_index} of {total_materials} critical materials requiring immediate attention.

For urgent assistance or questions, contact the Supply Chain Management team immediately.

Best regards,
Warehouse Operations Control
Supply Chain Management System

---
This is an automated alert generated by the Material Management System for Material {material.material}.
Physical inventory verification is critical for accurate stock management.
"""

    elif stakeholder == "supplier":
        subject = f"URGENT: Material Dispatch Status Request - {material.material} (Stock: {material.stock} units)"
        body = f"""Dear Valued Supplier Partner,

CRITICAL MATERIAL DISPATCH REQUEST - Individual Material Status Required

Material Details:
- Part Number: {material.material}
- Description: {material.material_short_text}
- Current Stock: {material.stock} units
- Status: {material.status}
- Coverage Status: {material.coverage_status}
- Coverage Days: {material.coverage_days}
- Urgency: HIGH (Below safety threshold)

REQUESTED ACTION: Immediate Material Quantity & Transit Status Report

IMMEDIATE INFORMATION REQUIRED FOR THIS MATERIAL:

1. MATERIAL QUANTITY STATUS:
   - Current production quantity for Part {material.material}
   - Finished goods inventory available for immediate dispatch
   - Work-in-progress quantities and expected completion dates
   - Raw material availability for continued production

2. DISPATCH STATUS INFORMATION:
   - Quantities already dispatched (if any) with dispatch dates
   - Planned dispatch quantities and scheduled dispatch dates
   - Materials currently in packaging/preparation for shipment
   - Shipping method and carrier information for dispatched materials

3. IN-TRANSIT STATUS TRACKING:
   - Tracking numbers for any shipments in transit
   - Current location and status of in-transit materials
   - Expected delivery dates and times
   - Any transit delays or issues requiring attention

4. PRODUCTION & DELIVERY SCHEDULE:
   - Next production run date for Material {material.material}
   - Estimated quantities from upcoming production
   - Revised delivery schedule if standard timelines cannot be met
   - Any supply chain constraints affecting production or delivery

RESPONSE DEADLINE: Please provide comprehensive status report within 1 HOUR for this material.

Include the following in your response:
- Detailed quantity and status information for Part {material.material}
- Tracking numbers and carrier details for in-transit shipments
- Updated delivery schedule with confirmed dates
- Any risk factors or potential delays

Priority Level: HIGH - This is {material_index} of {total_materials} critical materials requiring immediate attention.

For immediate escalation, please call our emergency procurement hotline.

Best regards,
Procurement Department
Supply Chain Management Team

---
This is a critical supply chain alert for Material {material.material} requiring immediate supplier response.
Timely information is essential for production continuity planning.
"""

    elif stakeholder == "logistics":
        subject = f"URGENT: Material Unloading Status - {material.material} (Stock: {material.stock} units)"
        body = f"""Dear Logistics Team,

CRITICAL MATERIAL RECEIVING ALERT - Individual Material Check Required

Material Details:
- Material Number: {material.material}
- Description: {material.material_short_text}
- Current Stock: {material.stock} units
- Status: {material.status}
- Coverage Status: {material.coverage_status}
- Coverage Days: {material.coverage_days}
- Priority: CRITICAL

REQUESTED ACTION: Immediate Unloading Status & Receiving Operations Check

IMMEDIATE ACTIONS REQUIRED FOR THIS MATERIAL:

1. RECEIVING DOCK STATUS CHECK:
   - Verify any pending deliveries for Material {material.material} at receiving docks
   - Check for trucks/containers containing this specific material awaiting unloading
   - Identify Material {material.material} in pending shipments
   - Prioritize unloading of this critical material

2. UNLOADING OPERATIONS STATUS:
   - Current unloading queue position for Material {material.material}
   - Estimated processing time for this material
   - Any delays or bottlenecks affecting this material's processing
   - Resource allocation for expedited processing

3. MATERIAL TRACKING & VERIFICATION:
   - Cross-reference incoming Material {material.material} against expected deliveries
   - Verify quantities being received vs. expected quantities
   - Check material condition upon receipt
   - Update receiving systems in real-time for this material

4. STAGING & TRANSFER STATUS:
   - Check if Material {material.material} is in receiving staging areas
   - Items requiring quality inspection before warehouse release
   - Transfer timeline from receiving to warehouse locations
   - Any customs or documentation holds affecting this material

5. EXPEDITED PROCESSING REQUEST:
   - Fast-track processing of Material {material.material}
   - Bypass non-essential inspection steps where possible
   - Arrange immediate warehouse transfer upon receipt
   - Coordinate with warehouse team for priority put-away

REPORTING DEADLINE: Provide status update within 30 MINUTES for this critical material.

Required Status Report Format for Material {material.material}:
- Current location (receiving dock, unloading bay, staging area)
- Estimated processing completion time
- Any issues or delays affecting this material
- Next steps and timeline for warehouse transfer

Priority Level: HIGH - This is {material_index} of {total_materials} critical materials requiring immediate attention.

For immediate assistance or to report urgent issues, contact the Operations Control Center.

Best regards,
Operations Control Center
Logistics Management Team

---
This is a time-critical logistics alert for Material {material.material} requiring immediate action.
Expedited processing of this material is essential to prevent production disruption.
"""
    
    return subject, body

def compose_email_content(stakeholder: str, materials: List[api_model.MaterialModel]) -> tuple:
    """Compose email content for different stakeholders"""
    material_count = len(materials)
    
    if stakeholder == "warehouse":
        subject = f"URGENT: Physical Inventory Check Required - {material_count} Materials Below 100 Units"
        body = f"""Dear Warehouse Team,

We have detected {material_count} materials with stock levels below 100 units that require immediate physical inventory verification.

REQUESTED ACTION: Physical Inventory Check

Materials Requiring Physical Count Verification:
"""
        for material in materials[:10]:  # Show up to 10 materials
            body += f"""
- Material: {material.material}
  Description: {material.material_short_text}
  System Stock: {material.stock} units
  Status: {material.status}
  Location: [Please verify physical location]
"""
        
        body += f"""
IMMEDIATE ACTIONS REQUIRED:

1. PHYSICAL COUNT VERIFICATION:
   - Conduct physical count for all listed materials
   - Verify actual quantities against system records
   - Check all storage locations (primary, overflow, quarantine areas)
   - Document any discrepancies found

2. STOCK CONDITION ASSESSMENT:
   - Inspect material condition (damaged, expired, quality issues)
   - Verify material identification and labeling
   - Check for any misplaced or mislabeled items

3. LOCATION VERIFICATION:
   - Confirm correct storage locations
   - Update location records if materials have been moved
   - Identify any materials in receiving/staging areas

4. REPORTING:
   - Complete physical inventory count sheet
   - Report discrepancies immediately via email
   - Provide updated stock levels within 4 hours
   - Include photos for any damaged or questionable materials

DEADLINE: Please complete physical inventory check and report results within 4 HOURS.

For urgent assistance or questions, contact the Supply Chain Management team immediately.

Best regards,
Warehouse Operations Control
Supply Chain Management System

---
This is an automated alert generated by the Material Management System.
Physical inventory verification is critical for accurate stock management.
"""

    elif stakeholder == "supplier":
        subject = f"URGENT: Material Dispatch & Transit Status Request - {material_count} Materials Below Safety Stock"
        body = f"""Dear Valued Supplier Partner,

We have identified {material_count} materials with critically low stock levels (below 100 units) that require immediate dispatch status information.

REQUESTED ACTION: Material Quantity & Transit Status Report

Materials Requiring Dispatch Status Information:
"""
        for material in materials[:10]:  # Show up to 10 materials
            body += f"""
- Part Number: {material.material}
  Description: {material.material_short_text}
  Current Stock: {material.stock} units
  Urgency: HIGH (Below safety threshold)
  Last Order Date: [Please provide]
"""
        
        body += f"""
IMMEDIATE INFORMATION REQUIRED:

1. MATERIAL QUANTITY STATUS:
   - Current production quantities for each listed material
   - Finished goods inventory available for immediate dispatch
   - Work-in-progress quantities and expected completion dates
   - Raw material availability for continued production

2. DISPATCH STATUS INFORMATION:
   - Quantities already dispatched (if any) with dispatch dates
   - Planned dispatch quantities and scheduled dispatch dates
   - Any materials currently in packaging/preparation for shipment
   - Shipping method and carrier information for dispatched materials

3. IN-TRANSIT STATUS TRACKING:
   - Tracking numbers for all shipments in transit
   - Current location and status of in-transit materials
   - Expected delivery dates and times
   - Any transit delays or issues requiring attention

4. PRODUCTION & DELIVERY SCHEDULE:
   - Next production run dates for these materials
   - Estimated quantities from upcoming production
   - Revised delivery schedules if standard timelines cannot be met
   - Any supply chain constraints affecting production or delivery

RESPONSE DEADLINE: Please provide comprehensive status report within 2 HOURS.

Include the following in your response:
- Excel/CSV file with detailed quantity and status information
- Tracking numbers and carrier details for in-transit shipments
- Updated delivery schedule with confirmed dates
- Any risk factors or potential delays

For immediate escalation, please call our emergency procurement hotline.

Best regards,
Procurement Department
Supply Chain Management Team

---
This is a critical supply chain alert requiring immediate supplier response.
Timely information is essential for production continuity planning.
"""

    elif stakeholder == "logistics":
        subject = f"URGENT: Material Unloading Status & Receiving Update - {material_count} Critical Materials"
        body = f"""Dear Logistics Team,

We have {material_count} materials with critically low stock levels (below 100 units) that may be awaiting unloading or processing at receiving docks.

REQUESTED ACTION: Unloading Status & Receiving Operations Check

Materials Requiring Unloading Status Verification:
"""
        for material in materials[:10]:  # Show up to 10 materials
            body += f"""
- Material: {material.material}
  Description: {material.material_short_text}
  Current Stock: {material.stock} units
  Expected Deliveries: [Please verify]
  Priority: CRITICAL
"""
        
        body += f"""
IMMEDIATE ACTIONS REQUIRED:

1. RECEIVING DOCK STATUS CHECK:
   - Verify all pending deliveries at receiving docks
   - Check for any trucks/containers awaiting unloading
   - Identify materials from our critical list in pending shipments
   - Prioritize unloading of critical materials

2. UNLOADING OPERATIONS STATUS:
   - Current unloading queue and estimated processing times
   - Materials currently being unloaded or processed
   - Any delays or bottlenecks in unloading operations
   - Resource availability (personnel, equipment) for expedited processing

3. MATERIAL TRACKING & VERIFICATION:
   - Cross-reference incoming materials against critical materials list
   - Verify quantities being received vs. expected quantities
   - Check material condition upon receipt
   - Update receiving systems in real-time

4. STAGING & TRANSFER STATUS:
   - Materials in receiving staging areas awaiting warehouse transfer
   - Items requiring quality inspection before warehouse release
   - Transfer timeline from receiving to warehouse locations
   - Any materials held up in customs or documentation processes

5. EXPEDITED PROCESSING REQUEST:
   - Fast-track processing of critical materials
   - Bypass non-essential inspection steps where possible
   - Arrange immediate warehouse transfer upon receipt
   - Coordinate with warehouse team for priority put-away

REPORTING DEADLINE: Provide status update within 1 HOUR.

Required Status Report Format:
- Materials currently at receiving docks (with ETA for processing)
- Materials being actively unloaded (with completion estimates)
- Materials in staging awaiting transfer (with transfer schedule)
- Any delays or issues requiring management intervention

For immediate assistance or to report urgent issues, contact the Operations Control Center.

Best regards,
Operations Control Center
Logistics Management Team

---
This is a time-critical logistics alert requiring immediate action.
Expedited processing of these materials is essential to prevent production disruption.
"""
    
    return subject, body

@app.get("/hello/{name}")
def read_root(name: str):
    return f"hello {name}"

@app.post("/open-explorer/")
def open_explorer(model: api_model.PathModel):
    os.startfile(model.path)
    return f"Opening {model.path}"

@app.post("/check-critical-materials/")
def check_and_store_critical_materials():
    """Check for materials with stock 100 or less and store in critical_materials.json"""
    try:
        critical_materials = check_critical_materials()
        
        # Convert to JSON serializable format
        materials_data = {
            "timestamp": datetime.now().isoformat(),
            "filter_criteria": "stock <= 100",
            "total_critical_materials": len(critical_materials),
            "materials": [material.model_dump() for material in critical_materials]
        }
        
        # Save to JSON file
        with open(CRITICAL_MATERIALS_JSON_PATH, 'w') as f:
            json.dump(materials_data, f, indent=2)
        
        # Calculate additional statistics
        stock_ranges = {
            "zero_stock": sum(1 for m in critical_materials if m.stock == 0),
            "low_stock_1_50": sum(1 for m in critical_materials if 1 <= m.stock <= 50),
            "medium_stock_51_99": sum(1 for m in critical_materials if 51 <= m.stock <= 99),
            "stock_100": sum(1 for m in critical_materials if m.stock == 100)
        }
        
        # Get status breakdown for materials with stock <= 100
        status_breakdown = {
            "red_count": sum(1 for m in critical_materials if m.status == "RED"),
            "yellow_count": sum(1 for m in critical_materials if m.status == "YELLOW"),
            "green_count": sum(1 for m in critical_materials if m.status == "GREEN")
        }
        
        return {
            "status": "success",
            "message": f"Found and stored {len(critical_materials)} materials with stock <= 100",
            "critical_materials_count": len(critical_materials),
            "filter_criteria": "stock <= 100",
            "stock_breakdown": stock_ranges,
            "status_breakdown": status_breakdown,
            "lowest_stock": min([m.stock for m in critical_materials]) if critical_materials else 0,
            "highest_stock": max([m.stock for m in critical_materials]) if critical_materials else 0
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/compose-and-send-emails/")
def compose_and_send_critical_material_emails():
    """Compose and send individual emails to all stakeholders for each critical material"""
    try:
        # Load critical materials
        if not os.path.exists(CRITICAL_MATERIALS_JSON_PATH):
            raise HTTPException(status_code=404, detail="Critical materials file not found. Run check-critical-materials first.")
        
        with open(CRITICAL_MATERIALS_JSON_PATH, 'r') as f:
            materials_data = json.load(f)
        
        materials = [api_model.MaterialModel(**m) for m in materials_data['materials']]
        email_info = load_email_info()
        
        sent_emails = []
        total_materials = len(materials)
        
        # Send individual email for each material to each stakeholder
        for material_index, material in enumerate(materials, 1):
            for stakeholder, email_address in email_info.items():
                try:
                    # Compose email content for this specific material
                    subject, body = compose_email_content_for_material(stakeholder, material, material_index, total_materials)
                    
                    # Send email using existing send_email function
                    email_model = api_model.EmailSendModel(
                        to=email_address,
                        subject=subject,
                        body=body
                    )
                    
                    send_email(email_model)
                    
                    sent_emails.append({
                        "stakeholder": stakeholder,
                        "email_address": email_address,
                        "material_number": material.material,
                        "material_description": material.material_short_text,
                        "material_stock": material.stock,
                        "subject": subject,
                        "status": "sent",
                        "timestamp": datetime.now().isoformat(),
                        "material_index": material_index,
                        "total_materials": total_materials
                    })
                    
                except Exception as e:
                    sent_emails.append({
                        "stakeholder": stakeholder,
                        "email_address": email_address,
                        "material_number": material.material,
                        "material_description": material.material_short_text,
                        "material_stock": material.stock,
                        "status": "failed",
                        "error": str(e),
                        "timestamp": datetime.now().isoformat(),
                        "material_index": material_index,
                        "total_materials": total_materials
                    })
        
        # Calculate summary statistics
        successful_sends = len([e for e in sent_emails if e["status"] == "sent"])
        failed_sends = len([e for e in sent_emails if e["status"] == "failed"])
        
        # Group by stakeholder for summary
        stakeholder_summary = {}
        for stakeholder in email_info.keys():
            stakeholder_emails = [e for e in sent_emails if e["stakeholder"] == stakeholder]
            stakeholder_summary[stakeholder] = {
                "total_emails": len(stakeholder_emails),
                "successful": len([e for e in stakeholder_emails if e["status"] == "sent"]),
                "failed": len([e for e in stakeholder_emails if e["status"] == "failed"])
            }
        
        return {
            "status": "completed",
            "approach": "individual_emails_per_material",
            "total_materials": total_materials,
            "total_stakeholders": len(email_info),
            "total_emails_attempted": len(sent_emails),
            "successful_sends": successful_sends,
            "failed_sends": failed_sends,
            "emails_per_stakeholder": total_materials,
            "stakeholder_summary": stakeholder_summary,
            "details": sent_emails
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/detect-stakeholder-emails/")
def detect_and_store_stakeholder_emails():
    """Detect and store incoming emails from stakeholders using pywin32 Outlook integration"""
    import pythoncom
    pythoncom.CoInitialize()

    try:
        print(f"[DEBUG] Starting stakeholder email detection...")
        print(f"[DEBUG] Email info CSV path: {EMAIL_INFO_CSV_PATH}")
        print(f"[DEBUG] Incoming emails JSON path: {INCOMING_EMAILS_JSON_PATH}")

        # Load stakeholder information from CSV
        email_info = load_email_info()
        print(f"[DEBUG] Loaded stakeholder emails: {email_info}")

        # Initialize Outlook application using pywin32
        print(f"[DEBUG] Initializing Outlook application...")
        try:
            outlook = win32com.client.Dispatch("Outlook.Application")
            namespace = outlook.GetNamespace("MAPI")
            print(f"[DEBUG] Outlook application initialized successfully")
        except Exception as outlook_error:
            print(f"[DEBUG] Error initializing Outlook: {str(outlook_error)}")
            raise HTTPException(status_code=500, detail=f"Failed to initialize Outlook: {str(outlook_error)}")

        # Get the default inbox
        try:
            inbox = namespace.GetDefaultFolder(6)  # 6 = Inbox
            messages = inbox.Items
            messages.Sort("ReceivedTime", True)  # Sort by received time, newest first
            print(f"[DEBUG] Inbox accessed successfully, total messages: {len(messages)}")
        except Exception as inbox_error:
            print(f"[DEBUG] Error accessing inbox: {str(inbox_error)}")
            raise HTTPException(status_code=500, detail=f"Failed to access Outlook inbox: {str(inbox_error)}")

        # Filter emails from stakeholders
        stakeholder_incoming = []
        processed_count = 0

        # Process recent emails (limit to 100 for performance)
        print(f"[DEBUG] Processing recent emails (limit: 100)...")
        for i, message in enumerate(messages):
            if i >= 100:  # Limit to recent 100 emails
                break

            if message.Class == 43:  # 43 = MailItem
                try:
                    sender_email = getattr(message, 'SenderEmailAddress', '')
                    sender_name = getattr(message, 'SenderName', '')
                    subject = getattr(message, 'Subject', '')
                    body = getattr(message, 'Body', '')
                    received_time = str(getattr(message, 'ReceivedTime', ''))

                    print(f"[DEBUG] Processing email {i}: {subject} from {sender_name} ({sender_email})")

                    # Extract email address from sender name if it contains email
                    if not sender_email and sender_name:
                        email_match = re.search(r'[\w\.-]+@[\w\.-]+', sender_name)
                        if email_match:
                            sender_email = email_match.group()
                            print(f"[DEBUG] Extracted email from sender name: {sender_email}")

                    # Check if email is from a stakeholder (improved matching logic)
                    stakeholder_type = None
                    for stakeholder, email_addr in email_info.items():
                        # Extract username from email address for flexible matching
                        email_username = email_addr.split('@')[0].lower()
                        sender_email_lower = sender_email.lower()
                        sender_name_lower = sender_name.lower()

                        # Multiple matching strategies
                        if (email_addr.lower() in sender_email_lower or
                            email_addr.lower() in sender_name_lower or
                            email_username in sender_email_lower or
                            email_username in sender_name_lower or
                            # Check for partial matches (e.g., "chandake" in "Chandake, Umesh")
                            any(part in sender_name_lower for part in email_username.split('.') if len(part) > 3)):
                            stakeholder_type = stakeholder
                            print(f"[DEBUG] Matched {stakeholder} stakeholder: {sender_name} ({sender_email})")
                            break

                    if stakeholder_type:
                        incoming_email = {
                            "id": f"{datetime.now().timestamp()}_{len(stakeholder_incoming)}",
                            "subject": subject,
                            "sender": sender_name,
                            "sender_email": sender_email,
                            "body": body,
                            "received_time": received_time,
                            "stakeholder_type": stakeholder_type,
                            "processed": False,
                            "detected_timestamp": datetime.now().isoformat()
                        }
                        stakeholder_incoming.append(incoming_email)
                        print(f"[DEBUG] Found stakeholder email from {stakeholder_type}: {subject}")

                    processed_count += 1

                except Exception as e:
                    print(f"[DEBUG] Error processing email {i}: {str(e)}")
                    continue

        print(f"[DEBUG] Processed {processed_count} emails, found {len(stakeholder_incoming)} stakeholder emails")

        # Load existing emails or create new file
        existing_emails = []
        if os.path.exists(INCOMING_EMAILS_JSON_PATH):
            try:
                with open(INCOMING_EMAILS_JSON_PATH, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
                    existing_emails = existing_data.get('emails', [])
            except Exception as e:
                print(f"[DEBUG] Error loading existing emails: {str(e)}")
                existing_emails = []

        # Add new emails (avoid duplicates based on subject and sender)
        existing_keys = [(e.get('subject', ''), e.get('sender', '')) for e in existing_emails]
        new_emails = []
        for email in stakeholder_incoming:
            email_key = (email['subject'], email['sender'])
            if email_key not in existing_keys:
                new_emails.append(email)

        all_emails = existing_emails + new_emails

        # Save to JSON file
        emails_data = {
            "timestamp": datetime.now().isoformat(),
            "total_emails": len(all_emails),
            "new_emails_detected": len(new_emails),
            "emails": all_emails
        }

        with open(INCOMING_EMAILS_JSON_PATH, 'w', encoding='utf-8') as f:
            json.dump(emails_data, f, indent=2, ensure_ascii=False)

        print(f"[DEBUG] Saved {len(new_emails)} new emails to {INCOMING_EMAILS_JSON_PATH}")

        # If new emails were detected, automatically analyze them
        analysis_results = None
        if new_emails:
            try:
                print(f"[DEBUG] Starting automatic analysis of {len(new_emails)} new emails")
                analysis_results = analyze_stakeholder_emails_internal(new_emails)
                print(f"[DEBUG] Analysis completed successfully")
            except Exception as e:
                print(f"[DEBUG] Error during automatic analysis: {str(e)}")
                analysis_results = {"error": str(e)}

        return {
            "status": "success",
            "message": f"Detected {len(new_emails)} new stakeholder emails and stored in {INCOMING_EMAILS_JSON_PATH}",
            "new_emails_count": len(new_emails),
            "total_emails_stored": len(all_emails),
            "stakeholder_breakdown": {
                stakeholder: len([e for e in new_emails if e['stakeholder_type'] == stakeholder])
                for stakeholder in email_info.keys()
            },
            "analysis_results": analysis_results,
            "files_updated": {
                "incoming_emails.json": True,
                "analysis_performed": new_emails is not None and len(new_emails) > 0
            }
        }

    except Exception as e:
        print(f"[DEBUG] Error in detect_and_store_stakeholder_emails: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        pythoncom.CoUninitialize()

def analyze_stakeholder_emails_internal(emails_to_analyze):
    """Internal function to analyze stakeholder emails and store results in output files"""
    try:
        # Initialize Azure OpenAI client
        client = initialize_azure_openai()

        # Initialize storage for different stakeholder types
        warehouse_discrepancies = []
        supplier_responses = []
        logistics_responses = []
        analyzed_results = []

        for email in emails_to_analyze:
            try:
                stakeholder_type = email.get('stakeholder_type', '')
                email_body = email.get('body', '')
                email_subject = email.get('subject', '')

                print(f"[DEBUG] Analyzing {stakeholder_type} email: {email_subject}")

                # Create stakeholder-specific analysis prompts
                if stakeholder_type == "warehouse":
                    analysis_prompt = f"""
                    Analyze this warehouse team email response about physical inventory check and extract discrepancy information:

                    Subject: {email_subject}
                    Body: {email_body}

                    Extract and return the following information in JSON format:
                    {{
                        "material_discrepancies": [
                            {{
                                "material_number": "material code if mentioned",
                                "system_quantity": "quantity in system if mentioned",
                                "physical_count": "actual physical count if mentioned",
                                "discrepancy_amount": "difference between system and physical",
                                "discrepancy_type": "shortage/overage/none",
                                "location": "storage location if mentioned",
                                "condition_issues": "any damage or quality issues mentioned",
                                "notes": "additional observations"
                            }}
                        ],
                        "overall_inventory_status": "general status reported",
                        "total_materials_checked": "number of materials physically counted",
                        "completion_status": "completed/partial/pending",
                        "reported_issues": ["list of any issues or problems mentioned"],
                        "next_actions": ["any follow-up actions mentioned"],
                        "reporting_timestamp": "when the count was performed if mentioned",
                        "responsible_person": "person who conducted the count if mentioned"
                    }}

                    Return only valid JSON without additional text.
                    """

                elif stakeholder_type == "supplier":
                    analysis_prompt = f"""
                    Analyze this supplier email response about material dispatch and transit status:

                    Subject: {email_subject}
                    Body: {email_body}

                    Extract and return the following information in JSON format:
                    {{
                        "material_status": [
                            {{
                                "material_number": "part number if mentioned",
                                "production_quantity": "quantity in production if mentioned",
                                "finished_goods_quantity": "ready to ship quantity if mentioned",
                                "dispatched_quantity": "already shipped quantity if mentioned",
                                "dispatch_date": "when material was shipped if mentioned",
                                "planned_dispatch_date": "future shipment date if mentioned",
                                "tracking_number": "shipment tracking number if provided",
                                "carrier": "shipping company if mentioned",
                                "estimated_delivery": "expected delivery date if mentioned"
                            }}
                        ],
                        "production_schedule": {{
                            "next_production_run": "next production date if mentioned",
                            "production_capacity": "daily/weekly production capacity if mentioned",
                            "lead_time": "production lead time if mentioned"
                        }},
                        "transit_information": [
                            {{
                                "tracking_number": "tracking number",
                                "current_status": "in transit status",
                                "current_location": "current shipment location if mentioned",
                                "expected_delivery": "delivery date/time"
                            }}
                        ],
                        "supply_constraints": ["any production or supply issues mentioned"],
                        "delivery_commitments": ["specific delivery promises made"],
                        "escalation_required": "true/false if urgent attention needed",
                        "response_timestamp": "when supplier provided this information"
                    }}

                    Return only valid JSON without additional text.
                    """

                elif stakeholder_type == "logistics":
                    analysis_prompt = f"""
                    Analyze this logistics team email response about unloading status and receiving operations:

                    Subject: {email_subject}
                    Body: {email_body}

                    Extract and return the following information in JSON format:
                    {{
                        "receiving_dock_status": {{
                            "trucks_waiting": "number of trucks in queue if mentioned",
                            "current_unloading": "what is currently being unloaded",
                            "estimated_completion": "when current unloading will finish"
                        }},
                        "material_unloading_status": [
                            {{
                                "material_number": "material code if mentioned",
                                "unloading_status": "not started/in progress/completed",
                                "quantity_received": "quantity unloaded if mentioned",
                                "quality_condition": "condition of received materials",
                                "location_assigned": "where material is staged/stored",
                                "completion_time": "when unloading was completed",
                                "issues_found": "any problems during unloading"
                            }}
                        ],
                        "processing_queue": {{
                            "materials_pending": ["list of materials awaiting processing"],
                            "estimated_processing_time": "time to process pending items",
                            "priority_materials": ["materials being fast-tracked"]
                        }},
                        "warehouse_transfer": {{
                            "materials_ready_for_transfer": ["materials ready to move to warehouse"],
                            "transfer_schedule": "when materials will be moved",
                            "transfer_delays": "any delays in warehouse transfer"
                        }},
                        "operational_issues": ["any equipment, personnel, or process issues"],
                        "expedited_actions": ["special measures taken for critical materials"],
                        "next_updates": "when next status update will be provided",
                        "contact_person": "person responsible for operations if mentioned"
                    }}

                    Return only valid JSON without additional text.
                    """

                else:
                    # Default analysis for unknown stakeholder types
                    analysis_prompt = f"""
                    Analyze this email and extract key information:

                    Subject: {email_subject}
                    Body: {email_body}

                    Extract and return general information in JSON format.
                    """

                # Call Azure OpenAI
                response = client.chat.completions.create(
                    model=AZURE_OPENAI_DEPLOYMENT,
                    messages=[
                        {"role": "system", "content": f"You are an AI assistant specialized in analyzing {stakeholder_type} emails for supply chain management. Extract structured information and return only valid JSON."},
                        {"role": "user", "content": analysis_prompt}
                    ],
                    temperature=0.1,
                    max_tokens=1500
                )

                # Parse AI response
                ai_response = response.choices[0].message.content

                try:
                    # Try to extract JSON from markdown code blocks if present
                    if "```json" in ai_response and "```" in ai_response:
                        # Extract JSON content between ```json and ```
                        start_marker = "```json"
                        end_marker = "```"
                        start_idx = ai_response.find(start_marker) + len(start_marker)
                        end_idx = ai_response.find(end_marker, start_idx)
                        if start_idx > len(start_marker) - 1 and end_idx > start_idx:
                            json_content = ai_response[start_idx:end_idx].strip()
                            extracted_info = json.loads(json_content)
                        else:
                            # Fallback to parsing the entire response
                            extracted_info = json.loads(ai_response)
                    else:
                        # Try parsing the entire response as JSON
                        extracted_info = json.loads(ai_response)
                except json.JSONDecodeError:
                    # Fallback if JSON parsing fails
                    extracted_info = {
                        "raw_analysis": ai_response,
                        "parsing_error": True,
                        "error_message": "Failed to parse AI response as JSON"
                    }

                # Create analysis result for general tracking
                analysis_result = {
                    "email_id": email['id'],
                    "stakeholder_type": stakeholder_type,
                    "subject": email_subject,
                    "sender": email.get('sender', ''),
                    "extracted_info": extracted_info,
                    "analysis_timestamp": datetime.now().isoformat(),
                    "confidence_score": 0.8 if not extracted_info.get('parsing_error') else 0.3
                }

                analyzed_results.append(analysis_result)

                # Store stakeholder-specific information
                stakeholder_entry = {
                    "email_id": email['id'],
                    "subject": email_subject,
                    "sender": email.get('sender', ''),
                    "received_time": email.get('received_time', ''),
                    "analysis_timestamp": datetime.now().isoformat(),
                    "extracted_data": extracted_info
                }

                if stakeholder_type == "warehouse":
                    warehouse_discrepancies.append(stakeholder_entry)
                elif stakeholder_type == "supplier":
                    supplier_responses.append(stakeholder_entry)
                elif stakeholder_type == "logistics":
                    logistics_responses.append(stakeholder_entry)

                # Mark email as processed in the original emails data
                email['processed'] = True

                print(f"[DEBUG] Successfully analyzed {stakeholder_type} email: {email_subject}")

            except Exception as e:
                print(f"[DEBUG] Error analyzing email {email.get('id', '')}: {str(e)}")
                # Create error entry
                error_entry = {
                    "email_id": email.get('id', ''),
                    "error": str(e),
                    "analysis_timestamp": datetime.now().isoformat()
                }
                analyzed_results.append(error_entry)
                continue

        # Save warehouse discrepancy information
        if warehouse_discrepancies:
            save_stakeholder_analysis_results("warehouse", warehouse_discrepancies, DISCREPANCY_JSON_PATH, "discrepancies")

        # Save supplier response information
        if supplier_responses:
            save_stakeholder_analysis_results("supplier", supplier_responses, SUPPLIER_RESPONSE_JSON_PATH, "responses")

        # Save logistics information
        if logistics_responses:
            save_stakeholder_analysis_results("logistics", logistics_responses, LOGISTICS_JSON_PATH, "logistics_updates")

        # Update the incoming emails file with processed status
        try:
            if os.path.exists(INCOMING_EMAILS_JSON_PATH):
                with open(INCOMING_EMAILS_JSON_PATH, 'r', encoding='utf-8') as f:
                    emails_data = json.load(f)

                # Mark analyzed emails as processed
                for email in emails_data.get('emails', []):
                    for analyzed_email in emails_to_analyze:
                        if email.get('id') == analyzed_email.get('id'):
                            email['processed'] = True
                            break

                # Save updated emails data
                with open(INCOMING_EMAILS_JSON_PATH, 'w', encoding='utf-8') as f:
                    json.dump(emails_data, f, indent=2, ensure_ascii=False)

                print(f"[DEBUG] Updated processed status for {len(emails_to_analyze)} emails in {INCOMING_EMAILS_JSON_PATH}")
        except Exception as e:
            print(f"[DEBUG] Error updating processed status: {str(e)}")

        return {
            "analyzed_count": len(analyzed_results),
            "stakeholder_breakdown": {
                "warehouse_discrepancies": len(warehouse_discrepancies),
                "supplier_responses": len(supplier_responses),
                "logistics_updates": len(logistics_responses)
            },
            "files_created": {
                "discrepancy.json": len(warehouse_discrepancies) > 0,
                "supplier_response.json": len(supplier_responses) > 0,
                "logistics.json": len(logistics_responses) > 0
            }
        }

    except Exception as e:
        print(f"[DEBUG] Error in analyze_stakeholder_emails_internal: {str(e)}")
        return {"error": str(e)}

def save_stakeholder_analysis_results(stakeholder_type, new_data, file_path, data_key):
    """Helper function to save stakeholder analysis results to JSON files"""
    try:
        # Load existing data if exists
        existing_data = []
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    existing_file_data = json.load(f)
                    existing_data = existing_file_data.get(data_key, [])
            except Exception as e:
                print(f"[DEBUG] Error loading existing {stakeholder_type} data: {str(e)}")
                existing_data = []

        # Combine existing and new data
        combined_data = {
            "last_updated": datetime.now().isoformat(),
            f"total_{data_key}": len(existing_data) + len(new_data),
            f"new_{data_key}_added": len(new_data),
            data_key: existing_data + new_data
        }

        # Save to file
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(combined_data, f, indent=2, ensure_ascii=False)

        print(f"[DEBUG] Saved {len(new_data)} new {stakeholder_type} analysis results to {file_path}")

    except Exception as e:
        print(f"[DEBUG] Error saving {stakeholder_type} analysis results: {str(e)}")

@app.post("/analyze-emails-with-ai/")
def analyze_emails_with_azure_openai():
    """Analyze emails using Azure OpenAI and store stakeholder-specific results in separate JSON files"""
    try:
        # Initialize Azure OpenAI client
        client = initialize_azure_openai()
        
        # Load incoming emails
        if not os.path.exists(INCOMING_EMAILS_JSON_PATH):
            raise HTTPException(status_code=404, detail="Incoming emails file not found. Run detect-stakeholder-emails first.")
        
        with open(INCOMING_EMAILS_JSON_PATH, 'r') as f:
            emails_data = json.load(f)
        
        emails = emails_data.get('emails', [])
        unprocessed_emails = [e for e in emails if not e.get('processed', False)]
        
        # Initialize storage for different stakeholder types
        warehouse_discrepancies = []
        supplier_responses = []
        logistics_responses = []
        analyzed_results = []
        
        for email in unprocessed_emails:
            try:
                stakeholder_type = email.get('stakeholder_type', '')
                email_body = email.get('body', '')
                email_subject = email.get('subject', '')
                
                # Create stakeholder-specific analysis prompts
                if stakeholder_type == "warehouse":
                    analysis_prompt = f"""
                    Analyze this warehouse team email response about physical inventory check and extract discrepancy information:
                    
                    Subject: {email_subject}
                    Body: {email_body}
                    
                    Extract and return the following information in JSON format:
                    {{
                        "material_discrepancies": [
                            {{
                                "material_number": "material code if mentioned",
                                "system_quantity": "quantity in system if mentioned",
                                "physical_count": "actual physical count if mentioned",
                                "discrepancy_amount": "difference between system and physical",
                                "discrepancy_type": "shortage/overage/none",
                                "location": "storage location if mentioned",
                                "condition_issues": "any damage or quality issues mentioned",
                                "notes": "additional observations"
                            }}
                        ],
                        "overall_inventory_status": "general status reported",
                        "total_materials_checked": "number of materials physically counted",
                        "completion_status": "completed/partial/pending",
                        "reported_issues": ["list of any issues or problems mentioned"],
                        "next_actions": ["any follow-up actions mentioned"],
                        "reporting_timestamp": "when the count was performed if mentioned",
                        "responsible_person": "person who conducted the count if mentioned"
                    }}
                    
                    Return only valid JSON without additional text.
                    """
                    
                elif stakeholder_type == "supplier":
                    analysis_prompt = f"""
                    Analyze this supplier email response about material dispatch and transit status:
                    
                    Subject: {email_subject}
                    Body: {email_body}
                    
                    Extract and return the following information in JSON format:
                    {{
                        "material_status": [
                            {{
                                "material_number": "part number if mentioned",
                                "production_quantity": "quantity in production if mentioned",
                                "finished_goods_quantity": "ready to ship quantity if mentioned",
                                "dispatched_quantity": "already shipped quantity if mentioned",
                                "dispatch_date": "when material was shipped if mentioned",
                                "planned_dispatch_date": "future shipment date if mentioned",
                                "tracking_number": "shipment tracking number if provided",
                                "carrier": "shipping company if mentioned",
                                "estimated_delivery": "expected delivery date if mentioned"
                            }}
                        ],
                        "production_schedule": {{
                            "next_production_run": "next production date if mentioned",
                            "production_capacity": "daily/weekly production capacity if mentioned",
                            "lead_time": "production lead time if mentioned"
                        }},
                        "transit_information": [
                            {{
                                "tracking_number": "tracking number",
                                "current_status": "in transit status",
                                "current_location": "current shipment location if mentioned",
                                "expected_delivery": "delivery date/time"
                            }}
                        ],
                        "supply_constraints": ["any production or supply issues mentioned"],
                        "delivery_commitments": ["specific delivery promises made"],
                        "escalation_required": "true/false if urgent attention needed",
                        "response_timestamp": "when supplier provided this information"
                    }}
                    
                    Return only valid JSON without additional text.
                    """
                    
                elif stakeholder_type == "logistics":
                    analysis_prompt = f"""
                    Analyze this logistics team email response about unloading status and receiving operations:
                    
                    Subject: {email_subject}
                    Body: {email_body}
                    
                    Extract and return the following information in JSON format:
                    {{
                        "receiving_dock_status": {{
                            "trucks_waiting": "number of trucks in queue if mentioned",
                            "current_unloading": "what is currently being unloaded",
                            "estimated_completion": "when current unloading will finish"
                        }},
                        "material_unloading_status": [
                            {{
                                "material_number": "material code if mentioned",
                                "unloading_status": "not started/in progress/completed",
                                "quantity_received": "quantity unloaded if mentioned",
                                "quality_condition": "condition of received materials",
                                "location_assigned": "where material is staged/stored",
                                "completion_time": "when unloading was completed",
                                "issues_found": "any problems during unloading"
                            }}
                        ],
                        "processing_queue": {{
                            "materials_pending": ["list of materials awaiting processing"],
                            "estimated_processing_time": "time to process pending items",
                            "priority_materials": ["materials being fast-tracked"]
                        }},
                        "warehouse_transfer": {{
                            "materials_ready_for_transfer": ["materials ready to move to warehouse"],
                            "transfer_schedule": "when materials will be moved",
                            "transfer_delays": "any delays in warehouse transfer"
                        }},
                        "operational_issues": ["any equipment, personnel, or process issues"],
                        "expedited_actions": ["special measures taken for critical materials"],
                        "next_updates": "when next status update will be provided",
                        "contact_person": "person responsible for operations if mentioned"
                    }}
                    
                    Return only valid JSON without additional text.
                    """
                
                else:
                    # Default analysis for unknown stakeholder types
                    analysis_prompt = f"""
                    Analyze this email and extract key information:
                    
                    Subject: {email_subject}
                    Body: {email_body}
                    
                    Extract and return general information in JSON format.
                    """
                
                # Call Azure OpenAI
                response = client.chat.completions.create(
                    model=AZURE_OPENAI_DEPLOYMENT,
                    messages=[
                        {"role": "system", "content": f"You are an AI assistant specialized in analyzing {stakeholder_type} emails for supply chain management. Extract structured information and return only valid JSON."},
                        {"role": "user", "content": analysis_prompt}
                    ],
                    temperature=0.1,
                    max_tokens=1500
                )
                
                # Parse AI response
                ai_response = response.choices[0].message.content

                try:
                    # Try to extract JSON from markdown code blocks if present
                    if "```json" in ai_response and "```" in ai_response:
                        # Extract JSON content between ```json and ```
                        start_marker = "```json"
                        end_marker = "```"
                        start_idx = ai_response.find(start_marker) + len(start_marker)
                        end_idx = ai_response.find(end_marker, start_idx)
                        if start_idx > len(start_marker) - 1 and end_idx > start_idx:
                            json_content = ai_response[start_idx:end_idx].strip()
                            extracted_info = json.loads(json_content)
                        else:
                            # Fallback to parsing the entire response
                            extracted_info = json.loads(ai_response)
                    else:
                        # Try parsing the entire response as JSON
                        extracted_info = json.loads(ai_response)
                except json.JSONDecodeError:
                    # Fallback if JSON parsing fails
                    extracted_info = {
                        "raw_analysis": ai_response,
                        "parsing_error": True,
                        "error_message": "Failed to parse AI response as JSON"
                    }
                
                # Create analysis result for general tracking
                analysis_result = {
                    "email_id": email['id'],
                    "stakeholder_type": stakeholder_type,
                    "subject": email_subject,
                    "sender": email.get('sender', ''),
                    "extracted_info": extracted_info,
                    "analysis_timestamp": datetime.now().isoformat(),
                    "confidence_score": 0.8 if not extracted_info.get('parsing_error') else 0.3
                }
                
                analyzed_results.append(analysis_result)
                
                # Store stakeholder-specific information
                stakeholder_entry = {
                    "email_id": email['id'],
                    "subject": email_subject,
                    "sender": email.get('sender', ''),
                    "received_time": email.get('received_time', ''),
                    "analysis_timestamp": datetime.now().isoformat(),
                    "extracted_data": extracted_info
                }
                
                if stakeholder_type == "warehouse":
                    warehouse_discrepancies.append(stakeholder_entry)
                elif stakeholder_type == "supplier":
                    supplier_responses.append(stakeholder_entry)
                elif stakeholder_type == "logistics":
                    logistics_responses.append(stakeholder_entry)
                
                # Mark email as processed
                email['processed'] = True
                
            except Exception as e:
                print(f"Error analyzing email {email.get('id', '')}: {str(e)}")
                # Create error entry
                error_entry = {
                    "email_id": email.get('id', ''),
                    "error": str(e),
                    "analysis_timestamp": datetime.now().isoformat()
                }
                analyzed_results.append(error_entry)
                continue
        
        # Update the emails file with processed status
        with open(INCOMING_EMAILS_JSON_PATH, 'w') as f:
            json.dump(emails_data, f, indent=2)
        
        # Save warehouse discrepancy information
        if warehouse_discrepancies:
            # Load existing discrepancy data if exists
            existing_discrepancies = []
            if os.path.exists(DISCREPANCY_JSON_PATH):
                try:
                    with open(DISCREPANCY_JSON_PATH, 'r') as f:
                        existing_data = json.load(f)
                        existing_discrepancies = existing_data.get('discrepancies', [])
                except:
                    pass
            
            discrepancy_data = {
                "last_updated": datetime.now().isoformat(),
                "total_discrepancy_reports": len(existing_discrepancies) + len(warehouse_discrepancies),
                "new_reports_added": len(warehouse_discrepancies),
                "discrepancies": existing_discrepancies + warehouse_discrepancies
            }
            
            with open(DISCREPANCY_JSON_PATH, 'w') as f:
                json.dump(discrepancy_data, f, indent=2)
        
        # Save supplier response information
        if supplier_responses:
            # Load existing supplier data if exists
            existing_responses = []
            if os.path.exists(SUPPLIER_RESPONSE_JSON_PATH):
                try:
                    with open(SUPPLIER_RESPONSE_JSON_PATH, 'r') as f:
                        existing_data = json.load(f)
                        existing_responses = existing_data.get('responses', [])
                except:
                    pass
            
            supplier_data = {
                "last_updated": datetime.now().isoformat(),
                "total_supplier_responses": len(existing_responses) + len(supplier_responses),
                "new_responses_added": len(supplier_responses),
                "responses": existing_responses + supplier_responses
            }
            
            with open(SUPPLIER_RESPONSE_JSON_PATH, 'w') as f:
                json.dump(supplier_data, f, indent=2)
        
        # Save logistics information
        if logistics_responses:
            # Load existing logistics data if exists
            existing_logistics = []
            if os.path.exists(LOGISTICS_JSON_PATH):
                try:
                    with open(LOGISTICS_JSON_PATH, 'r') as f:
                        existing_data = json.load(f)
                        existing_logistics = existing_data.get('logistics_updates', [])
                except:
                    pass
            
            logistics_data = {
                "last_updated": datetime.now().isoformat(),
                "total_logistics_updates": len(existing_logistics) + len(logistics_responses),
                "new_updates_added": len(logistics_responses),
                "logistics_updates": existing_logistics + logistics_responses
            }
            
            with open(LOGISTICS_JSON_PATH, 'w') as f:
                json.dump(logistics_data, f, indent=2)
        
        # Update cockpit data with general analysis results
        cockpit_data = {}
        if os.path.exists(COCKPIT_JSON_PATH):
            with open(COCKPIT_JSON_PATH, 'r') as f:
                cockpit_data = json.load(f)
        
        if 'email_analyses' not in cockpit_data:
            cockpit_data['email_analyses'] = []
        
        cockpit_data['email_analyses'].extend(analyzed_results)
        cockpit_data['last_analysis_timestamp'] = datetime.now().isoformat()
        cockpit_data['total_analyzed_emails'] = len(cockpit_data['email_analyses'])
        
        # Add summary statistics
        cockpit_data['analysis_summary'] = {
            "total_emails_analyzed": len(analyzed_results),
            "stakeholder_breakdown": {
                "warehouse_discrepancies": len(warehouse_discrepancies),
                "supplier_responses": len(supplier_responses),
                "logistics_updates": len(logistics_responses)
            },
            "files_updated": {
                "discrepancy_json": len(warehouse_discrepancies) > 0,
                "supplier_response_json": len(supplier_responses) > 0,
                "logistics_json": len(logistics_responses) > 0
            }
        }
        
        # Save updated cockpit data
        with open(COCKPIT_JSON_PATH, 'w') as f:
            json.dump(cockpit_data, f, indent=2)
        
        return {
            "status": "success",
            "message": f"Analyzed {len(analyzed_results)} emails and stored stakeholder-specific data",
            "analyzed_count": len(analyzed_results),
            "stakeholder_breakdown": {
                "warehouse_discrepancies": len(warehouse_discrepancies),
                "supplier_responses": len(supplier_responses),
                "logistics_updates": len(logistics_responses)
            },
            "files_created": {
                "discrepancy.json": len(warehouse_discrepancies) > 0,
                "supplier_response.json": len(supplier_responses) > 0,
                "logistics.json": len(logistics_responses) > 0
            },
            "total_cockpit_analyses": len(cockpit_data['email_analyses'])
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/update-cockpit-stats/")
def update_cockpit_statistics():
    """Update statistics for cockpit dashboard"""
    try:
        # Load data from various sources
        stats = {}
        
        # Material statistics
        try:
            df = pd.read_csv(DATA_CSV_PATH)
            stats['total_materials'] = len(df)
            stats['red_status_count'] = len(df[df['Status'] == 'RED'])
            stats['yellow_status_count'] = len(df[df['Status'] == 'YELLOW'])
            stats['green_status_count'] = len(df[df['Status'] == 'GREEN'])
        except:
            stats.update({
                'total_materials': 0,
                'red_status_count': 0,
                'yellow_status_count': 0,
                'green_status_count': 0
            })
        
        # Critical materials count
        if os.path.exists(CRITICAL_MATERIALS_JSON_PATH):
            with open(CRITICAL_MATERIALS_JSON_PATH, 'r') as f:
                critical_data = json.load(f)
                stats['critical_materials_count'] = critical_data.get('total_critical_materials', 0)
        else:
            stats['critical_materials_count'] = 0
        
        # Email statistics
        if os.path.exists(INCOMING_EMAILS_JSON_PATH):
            with open(INCOMING_EMAILS_JSON_PATH, 'r') as f:
                emails_data = json.load(f)
                stats['emails_received_count'] = emails_data.get('total_emails', 0)
        else:
            stats['emails_received_count'] = 0
        
        # Analysis statistics
        if os.path.exists(COCKPIT_JSON_PATH):
            with open(COCKPIT_JSON_PATH, 'r') as f:
                cockpit_data = json.load(f)
                stats['emails_analyzed_count'] = cockpit_data.get('total_analyzed_emails', 0)
        else:
            stats['emails_analyzed_count'] = 0
        
        stats['emails_sent_count'] = 0  # This would need to be tracked in email sending
        stats['last_updated'] = datetime.now().isoformat()
        
        # Load existing cockpit data or create new
        cockpit_data = {}
        if os.path.exists(COCKPIT_JSON_PATH):
            with open(COCKPIT_JSON_PATH, 'r') as f:
                cockpit_data = json.load(f)
        
        # Update with new stats
        cockpit_data['dashboard_stats'] = stats
        
        # Save updated cockpit data
        with open(COCKPIT_JSON_PATH, 'w') as f:
            json.dump(cockpit_data, f, indent=2)
        
        return {
            "status": "success",
            "message": "Cockpit statistics updated successfully",
            "stats": stats
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/cockpit-data/")
def get_cockpit_data():
    """Get all cockpit data for dashboard updates"""
    try:
        if not os.path.exists(COCKPIT_JSON_PATH):
            return {"status": "no_data", "message": "Cockpit data not available"}
        
        with open(COCKPIT_JSON_PATH, 'r') as f:
            cockpit_data = json.load(f)
        
        return {
            "status": "success",
            "data": cockpit_data
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/send-email/")
def send_email(model: api_model.EmailSendModel):
    import pythoncom
    pythoncom.CoInitialize()
    try:
        outlook = win32com.client.Dispatch('outlook.application')
        namespace = outlook.GetNamespace("MAPI")
        # Use provided account or default
        account_address = getattr(model, 'account', None) or DEFAULT_EMAIL_ACCOUNT
        account = None
        for acc in namespace.Accounts:
            if acc.SmtpAddress.lower() == account_address.lower():
                account = acc
                break
        if not account:
            raise HTTPException(status_code=404, detail=f"Account {account_address} not found.")
        mail = outlook.CreateItem(0)
        mail._oleobj_.Invoke(*(64209, 0, 8, 0, account))  # Set the sending account
        mail.To = model.to
        mail.Subject = model.subject
        mail.Body = model.body
        if model.cc:
            mail.CC = model.cc
        if model.attachments:
            for file in model.attachments:
                mail.Attachments.Add(file)
        mail.Send()
        return {"status": "Email sent successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        pythoncom.CoUninitialize()

@app.get("/receive-emails/", response_model=List[api_model.EmailReceiveModel])
def receive_emails(limit: int = 10, account: str = None):
    import pythoncom
    pythoncom.CoInitialize()
    try:
        outlook = win32com.client.Dispatch("Outlook.Application")
        namespace = outlook.GetNamespace("MAPI")
        inbox = None
        account_address = account or DEFAULT_EMAIL_ACCOUNT

        print(f"[DEBUG] Looking for inbox for account: {account_address}")
        print("[DEBUG] Available accounts:")
        for acc in namespace.Accounts:
            print(f" - {acc.DisplayName} ({acc.SmtpAddress})")

        print("[DEBUG] Available stores:")
        for store in namespace.Stores:
            print(f" - {store.DisplayName}")

        found = False
        for acc in namespace.Accounts:
            if acc.SmtpAddress.lower() == account_address.lower():
                # Find the inbox for this account using GetDefaultFolder(6)
                for store in namespace.Stores:
                    if store.DisplayName == acc.DisplayName:
                        inbox = store.GetDefaultFolder(6)  # 6 = Inbox
                        found = True
                        print(f"[DEBUG] Found inbox for account {acc.DisplayName}")
                        break
            if found:
                break
        if not inbox:
            raise HTTPException(status_code=404, detail=f"Inbox for account {account_address} not found.")
        messages = inbox.Items
        messages.Sort("ReceivedTime", True)
        emails = []
        for i, message in enumerate(messages):
            if i >= limit:
                break
            if message.Class == 43:  # 43 = MailItem
                emails.append({
                    "subject": getattr(message, 'Subject', None),
                    "sender": getattr(message, 'SenderName', None),
                    "body": getattr(message, 'Body', None),
                    "received_time": str(getattr(message, 'ReceivedTime', None)),
                })
        return emails
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        pythoncom.CoUninitialize()

# Background task to run full analysis pipeline
@app.post("/run-full-analysis-pipeline/")
def run_full_analysis_pipeline(background_tasks: BackgroundTasks):
    """Run the complete analysis pipeline as a background task"""
    
    def pipeline():
        try:
            # Step 1: Check critical materials
            check_and_store_critical_materials()
            
            # Step 2: Compose and send emails
            compose_and_send_critical_material_emails()
            
            # Step 3: Detect stakeholder emails
            detect_and_store_stakeholder_emails()
            
            # Step 4: Analyze emails with AI
            analyze_emails_with_azure_openai()
            
            # Step 5: Update cockpit stats
            update_cockpit_statistics()
            
        except Exception as e:
            print(f"Pipeline error: {str(e)}")
    
    background_tasks.add_task(pipeline)
    
    return {
        "status": "started",
        "message": "Full analysis pipeline started in background"
    }

if __name__ == "__main__":
    import uvicorn
    print(f"Starting FastAPI server on {HOST}:{PORT}")
    print("Server will be available at: http://127.0.0.1:7777")
    uvicorn.run(app, host=HOST, port=PORT, reload=False)