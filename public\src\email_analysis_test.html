<!DOCTYPE html>
<html class="dark">
<head>
    <meta charset="UTF-8">
    <title>Email Analysis Test - Azure OpenAI</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background-image: url('../../GiRa_background.svg');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            background-attachment: fixed;
            min-height: 100vh;
        }

        .glass-panel {
            background: rgba(31, 41, 55, 0.2);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.05);
        }

        .glass-card {
            background: rgba(55, 65, 81, 0.2);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.05);
        }
    </style>
</head>
<body class="bg-gray-900 text-white">
    <!-- Header -->
    <header class="glass-panel border-b border-gray-700/50 px-6 py-4">
        <div class="flex items-center justify-between">
            <h1 class="text-2xl font-bold text-white">Email Analysis Test</h1>
            <img src="../assets/media/logo/mb_star.png" alt="Mercedes-Benz" class="h-10 w-10">
        </div>
    </header>

    <div class="max-w-7xl mx-auto p-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Input Section -->
            <div class="glass-card rounded-xl p-6">
                <h2 class="text-lg font-semibold mb-4">Test Email Input</h2>
                
                <!-- Email Selection -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-300 mb-2">Outlook Email Account</label>
                    <div class="flex space-x-2">
                        <input type="email" 
                            id="emailAccount" 
                            class="flex-1 bg-gray-800/50 text-white rounded-lg px-3 py-2 border border-gray-700 focus:border-purple-500 focus:ring-1 focus:ring-purple-500 outline-none"
                            placeholder="<EMAIL>"
                            value="<EMAIL>">
                        <button id="fetchEmails" 
                            class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center space-x-2">
                            <span>Fetch Emails</span>
                            <div id="fetchSpinner" class="hidden">
                                <svg class="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </div>
                        </button>
                    </div>
                    <div id="emailList" class="space-y-2 max-h-96 overflow-y-auto">
                        <div class="p-4 bg-gray-800/50 rounded-lg">
                            <p class="text-gray-400">Click "Fetch Emails" to load recent emails...</p>
                        </div>
                    </div>
                </div>

                <!-- Selected Email Preview -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-300 mb-2">Selected Email Content</label>
                    <div id="selectedEmail" class="p-4 bg-gray-800/50 rounded-lg min-h-[200px]">
                        <p class="text-gray-400">Select an email to preview its content...</p>
                    </div>
                </div>

                <!-- Analysis Button -->
                <button id="analyzeButton" 
                    class="w-full py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors flex items-center justify-center space-x-2">
                    <span>Analyze Selected Email</span>
                    <div id="loadingSpinner" class="hidden">
                        <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </div>
                </button>
            </div>

            <!-- Results Section -->
            <div class="glass-card rounded-xl p-6">
                <h2 class="text-lg font-semibold mb-4">Analysis Results</h2>
                <div id="resultsContainer" class="space-y-4">
                    <div class="p-4 bg-gray-800/50 rounded-lg">
                        <p class="text-gray-400">Analysis results will appear here...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://127.0.0.1:7777';

        const fetchEmailsButton = document.getElementById('fetchEmails');
        const fetchSpinner = document.getElementById('fetchSpinner');
        const emailList = document.getElementById('emailList');
        const selectedEmailDiv = document.getElementById('selectedEmail');
        const analyzeButton = document.getElementById('analyzeButton');
        const loadingSpinner = document.getElementById('loadingSpinner');
        const resultsContainer = document.getElementById('resultsContainer');

        let emails = [];
        let selectedEmail = null;

        // Fetch recent emails from backend
        async function fetchEmails() {
            fetchEmailsButton.disabled = true;
            fetchSpinner.classList.remove('hidden');
            emailList.innerHTML = '<div class="p-4 bg-gray-800/50 rounded-lg"><p class="text-gray-400">Loading emails...</p></div>';
            try {
                const emailAccount = document.getElementById('emailAccount').value.trim();
                const url = new URL(`${API_BASE_URL}/receive-emails/`);
                url.searchParams.append('limit', 20);
                if (emailAccount) {
                    url.searchParams.append('account', emailAccount);
                }
                const response = await fetch(url.toString());
                if (!response.ok) {
                    throw new Error(`Failed to fetch emails: ${response.statusText}`);
                }
                emails = await response.json();
                if (emails.length === 0) {
                    emailList.innerHTML = '<div class="p-4 bg-gray-800/50 rounded-lg"><p class="text-gray-400">No emails found.</p></div>';
                    return;
                }
                renderEmailList();
            } catch (error) {
                emailList.innerHTML = `<div class="p-4 bg-red-900/50 border border-red-700 rounded-lg"><p class="text-red-400">Error: ${error.message}</p></div>`;
            } finally {
                fetchEmailsButton.disabled = false;
                fetchSpinner.classList.add('hidden');
            }
        }

        // Render email list UI
        function renderEmailList() {
            emailList.innerHTML = '';
            emails.forEach((email, index) => {
                const emailItem = document.createElement('div');
                emailItem.className = 'p-3 bg-gray-800/50 rounded-lg cursor-pointer hover:bg-purple-700/50 transition-colors';
                emailItem.textContent = `${email.subject || '(No Subject)'} - ${email.sender || '(Unknown Sender)'}`;
                emailItem.addEventListener('click', () => selectEmail(index));
                emailList.appendChild(emailItem);
            });
        }

        // Select an email and show preview
        function selectEmail(index) {
            selectedEmail = emails[index];
            selectedEmailDiv.innerHTML = `
                <p><strong>Subject:</strong> ${selectedEmail.subject || '(No Subject)'}</p>
                <p><strong>Sender:</strong> ${selectedEmail.sender || '(Unknown Sender)'}</p>
                <pre class="whitespace-pre-wrap mt-2">${selectedEmail.body || '(No Content)'}</pre>
            `;
        }

        // Analyze the currently selected email content
        analyzeButton.addEventListener('click', async () => {
            if (!selectedEmail) {
                resultsContainer.innerHTML = `<div class="p-4 bg-red-900/50 border border-red-700 rounded-lg"><p class="text-red-400">Please select an email to analyze.</p></div>`;
                return;
            }

            analyzeButton.disabled = true;
            loadingSpinner.classList.remove('hidden');
            resultsContainer.innerHTML = '<div class="p-4 bg-gray-800/50 rounded-lg"><p class="text-gray-400">Analyzing email...</p></div>';

            try {
                // Store the email
                const storeResponse = await fetch(`${API_BASE_URL}/detect-stakeholder-emails/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(selectedEmail)
                });

                if (!storeResponse.ok) {
                    throw new Error(`Failed to store email: ${storeResponse.statusText}`);
                }

                // Analyze the email
                const analysisResponse = await fetch(`${API_BASE_URL}/analyze-emails-with-ai/`, {
                    method: 'POST'
                });

                if (!analysisResponse.ok) {
                    throw new Error(`Failed to analyze email: ${analysisResponse.statusText}`);
                }

                const analysisResult = await analysisResponse.json();

                // Display results
                resultsContainer.innerHTML = `
                    <div class="space-y-4">
                        <div class="p-4 bg-gray-800/50 rounded-lg">
                            <h3 class="text-md font-semibold text-purple-400 mb-2">Analysis Summary</h3>
                            <div class="space-y-2">
                                <p><span class="text-gray-400">Analyzed Count:</span> ${analysisResult.analyzed_count}</p>
                                <p><span class="text-gray-400">Status:</span> ${analysisResult.status}</p>
                            </div>
                        </div>
                        <div class="p-4 bg-gray-800/50 rounded-lg">
                            <h3 class="text-md font-semibold text-purple-400 mb-2">Stakeholder Breakdown</h3>
                            <div class="space-y-2">
                                ${Object.entries(analysisResult.stakeholder_breakdown || {}).map(([key, value]) => 
                                    `<p><span class="text-gray-400">${key}:</span> ${value}</p>`
                                ).join('')}
                            </div>
                        </div>
                        <div class="p-4 bg-gray-800/50 rounded-lg">
                            <h3 class="text-md font-semibold text-purple-400 mb-2">Files Updated</h3>
                            <div class="space-y-2">
                                ${Object.entries(analysisResult.files_created || {}).map(([key, value]) => 
                                    `<p><span class="text-gray-400">${key}:</span> ${value ? '✓' : '✗'}</p>`
                                ).join('')}
                            </div>
                        </div>
                    </div>
                `;
            } catch (error) {
                resultsContainer.innerHTML = `
                    <div class="p-4 bg-red-900/50 border border-red-700 rounded-lg">
                        <p class="text-red-400">Error: ${error.message}</p>
                    </div>
                `;
            } finally {
                analyzeButton.disabled = false;
                loadingSpinner.classList.add('hidden');
            }
        });

        fetchEmailsButton.addEventListener('click', fetchEmails);
    </script>
</body>
</html>
