#!/usr/bin/env python3
"""
Test script for the /analyze-emails-with-ai/ endpoint
"""

import requests
import json
import sys
import os

# Add the engine directory to the path so we can import the API
sys.path.append(os.path.join(os.path.dirname(__file__), 'engine'))

def test_analyze_emails_endpoint():
    """Test the analyze emails endpoint"""
    
    # Test with direct function call first
    print("=== Testing analyze_emails_with_azure_openai function directly ===")
    
    try:
        from api import analyze_emails_with_azure_openai
        result = analyze_emails_with_azure_openai()
        print("✅ Direct function call successful!")
        print(f"📊 Result: {json.dumps(result, indent=2)}")
        return True
        
    except Exception as e:
        print(f"❌ Direct function call failed: {str(e)}")
        print(f"Error type: {type(e).__name__}")
        
        # Check if it's an Azure OpenAI configuration issue
        if "Azure OpenAI configuration missing" in str(e):
            print("🔧 Azure OpenAI configuration issue detected")
            print("Checking environment variables...")
            
            from dotenv import load_dotenv
            load_dotenv()
            
            endpoint = os.getenv("AZURE_OPENAI_ENDPOINT", "")
            api_key = os.getenv("AZURE_OPENAI_API_KEY", "")
            
            print(f"AZURE_OPENAI_ENDPOINT: {'✅ Set' if endpoint else '❌ Missing'}")
            print(f"AZURE_OPENAI_API_KEY: {'✅ Set' if api_key else '❌ Missing'}")
            
        elif "Incoming emails file not found" in str(e):
            print("📧 Incoming emails file issue - this should not happen as we created test data")
            
        return False

def test_with_mock_azure_openai():
    """Test with mocked Azure OpenAI to avoid API costs"""
    print("\n=== Testing with Mock Azure OpenAI ===")
    
    try:
        # Import and patch the Azure OpenAI client
        import sys
        sys.path.append(os.path.join(os.path.dirname(__file__), 'engine'))
        
        # Create a mock response
        class MockChoice:
            def __init__(self, content):
                self.message = MockMessage(content)
        
        class MockMessage:
            def __init__(self, content):
                self.content = content
        
        class MockResponse:
            def __init__(self, content):
                self.choices = [MockChoice(content)]
        
        class MockAzureOpenAI:
            def __init__(self, *args, **kwargs):
                pass
            
            class chat:
                class completions:
                    @staticmethod
                    def create(*args, **kwargs):
                        # Return different mock responses based on stakeholder type
                        messages = kwargs.get('messages', [])
                        user_message = messages[-1]['content'] if messages else ""
                        
                        if 'warehouse' in user_message.lower():
                            mock_response = {
                                "material_discrepancies": [
                                    {"material_code": "A123", "system_quantity": 50, "physical_quantity": 45, "discrepancy": -5},
                                    {"material_code": "C789", "system_quantity": 75, "physical_quantity": 78, "discrepancy": 3}
                                ],
                                "overall_inventory_status": "Discrepancies found in 2 out of 15 materials",
                                "total_materials_checked": 15,
                                "completion_status": "completed",
                                "reported_issues": ["System error suspected for Material D111"],
                                "next_actions": ["Immediate system adjustment for discrepant materials"],
                                "responsible_person": "John Smith - Warehouse Supervisor"
                            }
                        elif 'supplier' in user_message.lower():
                            mock_response = {
                                "materials_status": [
                                    {"material_code": "X100", "dispatch_status": "dispatched", "tracking_number": "TR123456789", "expected_delivery": "2025-01-28"},
                                    {"material_code": "Y200", "dispatch_status": "dispatched", "tracking_number": "TR987654321", "expected_delivery": "2025-01-29"},
                                    {"material_code": "Z300", "dispatch_status": "delayed", "new_dispatch_date": "2025-01-30"}
                                ],
                                "overall_dispatch_status": "partially_dispatched",
                                "total_materials": 3,
                                "carrier_information": "DHL Express",
                                "delivery_performance": "98.5% on-time delivery",
                                "quality_status": "All materials meeting specifications"
                            }
                        elif 'logistics' in user_message.lower():
                            mock_response = {
                                "unloading_status": [
                                    {"shipment_id": "SH001", "status": "completed", "units_received": 150, "damage_reported": False},
                                    {"shipment_id": "SH002", "status": "in_progress", "completion_percentage": 75},
                                    {"shipment_id": "SH003", "status": "delayed", "reason": "dock availability"}
                                ],
                                "dock_status": "2 of 3 docks operational",
                                "total_materials_processed": 425,
                                "quality_issues": "None reported",
                                "operational_efficiency": "92%"
                            }
                        else:
                            mock_response = {"general_info": "Email analyzed successfully"}
                        
                        return MockResponse(json.dumps(mock_response))
        
        # Patch the Azure OpenAI import
        import api
        original_azure_openai = api.AzureOpenAI
        api.AzureOpenAI = MockAzureOpenAI
        
        try:
            result = api.analyze_emails_with_azure_openai()
            print("✅ Mock test successful!")
            print(f"📊 Result: {json.dumps(result, indent=2)}")
            return True
        finally:
            # Restore original
            api.AzureOpenAI = original_azure_openai
            
    except Exception as e:
        print(f"❌ Mock test failed: {str(e)}")
        return False

def check_generated_files():
    """Check if the expected output files were generated"""
    print("\n=== Checking Generated Files ===")
    
    expected_files = [
        'discrepancy.json',
        'supplier_response.json', 
        'logistics.json',
        'cockpit.json'
    ]
    
    for filename in expected_files:
        if os.path.exists(filename):
            print(f"✅ {filename} exists")
            try:
                with open(filename, 'r') as f:
                    data = json.load(f)
                    print(f"   📄 Contains {len(data)} items" if isinstance(data, list) else f"   📄 Contains data")
            except Exception as e:
                print(f"   ❌ Error reading {filename}: {e}")
        else:
            print(f"❌ {filename} not found")

if __name__ == "__main__":
    print("🧪 Testing POST /analyze-emails-with-ai/ endpoint")
    print("=" * 50)
    
    # Test 1: Direct function call
    success1 = test_analyze_emails_endpoint()
    
    # Test 2: Mock test if direct test failed due to Azure OpenAI
    if not success1:
        success2 = test_with_mock_azure_openai()
    else:
        success2 = True
    
    # Test 3: Check generated files
    check_generated_files()
    
    print("\n" + "=" * 50)
    if success1 or success2:
        print("🎉 Testing completed successfully!")
    else:
        print("❌ Testing failed - check the errors above")
