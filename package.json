{"name": "fast-api-electron-jse", "version": "0.1.0", "description": "A boilerplate for ElectronJS frontend and Python Backend", "main": "public/assets/js/main.js", "scripts": {"py-install": "pip install -r requirements.txt", "py-build": "python -m PyInstaller --workpath=engine/build --distpath=engine/dist api.spec", "electron-dev": "electron .", "electron-build": "electron-builder --config electron-builder.config.json"}, "repository": {"type": "git", "url": "git+https://github.com/gnoviawan/fast-api-electron-js.git"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/gnoviawan/fast-api-electron-js/issues"}, "homepage": "https://github.com/gnoviawan/fast-api-electron-js#readme", "devDependencies": {"axios": "^0.21.1", "electron": "^13.1.2", "electron-builder": "^22.11.7", "electron-reloader": "^1.2.1", "python-shell": "^3.0.0"}, "dependencies": {"axios": "^0.21.1", "electron-is-dev": "^1.2.0"}}