#!/usr/bin/env python3
"""
Test script for the SC Copilot KI email workflow
This script tests the email detection and analysis functionality
"""

import os
import json
import requests
import time
from datetime import datetime

# Configuration
API_BASE_URL = "http://127.0.0.1:7777"
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))

def test_api_connectivity():
    """Test if the API server is running"""
    try:
        response = requests.get(f"{API_BASE_URL}/hello/connectivity-test")
        if response.status_code == 200:
            print("✅ API server is running")
            return True
        else:
            print(f"❌ API server returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API server is not accessible: {str(e)}")
        return False

def test_file_existence():
    """Test if required files exist"""
    files_to_check = [
        "email_info.csv",
        "data.csv"
    ]
    
    all_exist = True
    for file_path in files_to_check:
        full_path = os.path.join(PROJECT_ROOT, file_path)
        if os.path.exists(full_path):
            print(f"✅ {file_path} exists")
        else:
            print(f"❌ {file_path} not found at {full_path}")
            all_exist = False
    
    return all_exist

def test_email_info_csv():
    """Test reading email_info.csv"""
    try:
        import pandas as pd
        email_info_path = os.path.join(PROJECT_ROOT, "email_info.csv")
        df = pd.read_csv(email_info_path)
        
        print(f"✅ email_info.csv loaded successfully")
        print(f"   📊 Found {len(df)} stakeholder entries:")
        
        for _, row in df.iterrows():
            stakeholder = row.get('stakeholder', 'N/A')
            email = row.get('email_address', 'N/A')
            department = row.get('department', 'N/A')
            print(f"   - {stakeholder}: {email} ({department})")
        
        return True
    except Exception as e:
        print(f"❌ Error reading email_info.csv: {str(e)}")
        return False

def test_step_1_critical_materials():
    """Test Step 1: Check Critical Materials"""
    try:
        print("\n🔄 Testing Step 1: Check Critical Materials")
        response = requests.post(f"{API_BASE_URL}/check-critical-materials/")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Step 1 successful")
            print(f"   📊 Found {result.get('total_critical_materials', 0)} critical materials")
            return True
        else:
            print(f"❌ Step 1 failed with status {response.status_code}: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Step 1 error: {str(e)}")
        return False

def test_step_2_compose_emails():
    """Test Step 2: Compose and Send Emails"""
    try:
        print("\n🔄 Testing Step 2: Compose and Send Emails")
        response = requests.post(f"{API_BASE_URL}/compose-and-send-emails/")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Step 2 successful")
            print(f"   📧 Attempted to send {result.get('total_emails_attempted', 0)} emails")
            print(f"   ✅ Successful sends: {result.get('successful_sends', 0)}")
            print(f"   ❌ Failed sends: {result.get('failed_sends', 0)}")
            return True
        else:
            print(f"❌ Step 2 failed with status {response.status_code}: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Step 2 error: {str(e)}")
        return False

def test_step_3a_detect_emails():
    """Test Step 3a: Detect Stakeholder Emails"""
    try:
        print("\n🔄 Testing Step 3a: Detect Stakeholder Emails")
        response = requests.post(f"{API_BASE_URL}/detect-stakeholder-emails/")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Step 3a successful")
            print(f"   📨 New emails detected: {result.get('new_emails_count', 0)}")
            print(f"   📊 Total emails stored: {result.get('total_emails_stored', 0)}")
            
            # Check if incoming_emails.json was created/updated
            incoming_emails_path = os.path.join(PROJECT_ROOT, "incoming_emails.json")
            if os.path.exists(incoming_emails_path):
                print(f"   ✅ incoming_emails.json file exists")
                with open(incoming_emails_path, 'r') as f:
                    emails_data = json.load(f)
                    print(f"   📧 File contains {emails_data.get('total_emails', 0)} emails")
            else:
                print(f"   ❌ incoming_emails.json file not found")
            
            return True
        else:
            print(f"❌ Step 3a failed with status {response.status_code}: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Step 3a error: {str(e)}")
        return False

def test_step_3b_analyze_emails():
    """Test Step 3b: Analyze Emails with AI"""
    try:
        print("\n🔄 Testing Step 3b: Analyze Emails with AI")
        response = requests.post(f"{API_BASE_URL}/analyze-emails-with-ai/")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Step 3b successful")
            print(f"   🧠 Emails analyzed: {result.get('analyzed_count', 0)}")
            
            stakeholder_breakdown = result.get('stakeholder_breakdown', {})
            for stakeholder, count in stakeholder_breakdown.items():
                print(f"   - {stakeholder}: {count} emails")
            
            return True
        else:
            print(f"❌ Step 3b failed with status {response.status_code}: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Step 3b error: {str(e)}")
        return False

def test_step_4_update_stats():
    """Test Step 4: Update Cockpit Stats"""
    try:
        print("\n🔄 Testing Step 4: Update Cockpit Stats")
        response = requests.get(f"{API_BASE_URL}/update-cockpit-stats/")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Step 4 successful")
            print(f"   📊 Dashboard updated with latest statistics")
            return True
        else:
            print(f"❌ Step 4 failed with status {response.status_code}: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Step 4 error: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 SC Copilot KI Email Workflow Test")
    print("=" * 50)
    
    # Test prerequisites
    print("\n📋 Testing Prerequisites...")
    if not test_api_connectivity():
        print("❌ Cannot proceed without API server")
        return
    
    if not test_file_existence():
        print("❌ Cannot proceed without required files")
        return
    
    if not test_email_info_csv():
        print("❌ Cannot proceed without valid email_info.csv")
        return
    
    print("\n✅ All prerequisites met, starting workflow tests...")
    
    # Test the complete workflow
    success_count = 0
    total_tests = 5
    
    if test_step_1_critical_materials():
        success_count += 1
    
    if test_step_2_compose_emails():
        success_count += 1
    
    if test_step_3a_detect_emails():
        success_count += 1
    
    if test_step_3b_analyze_emails():
        success_count += 1
    
    if test_step_4_update_stats():
        success_count += 1
    
    # Summary
    print("\n" + "=" * 50)
    print(f"📊 Test Summary: {success_count}/{total_tests} tests passed")
    
    if success_count == total_tests:
        print("🎉 All tests passed! The SC Copilot KI workflow is working correctly.")
    else:
        print(f"⚠️  {total_tests - success_count} test(s) failed. Please check the errors above.")
    
    print("\n📝 Next Steps:")
    print("1. Check the generated files (critical_materials.json, incoming_emails.json)")
    print("2. Verify Azure OpenAI configuration if email analysis failed")
    print("3. Check Outlook integration if email detection failed")
    print("4. Run the workflow from the web interface")

if __name__ == "__main__":
    main()
