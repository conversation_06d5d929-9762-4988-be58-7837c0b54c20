<!DOCTYPE html>
<html class="dark">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Cockpit Dashboard - Python FAST API x Electron JS</title>

  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- Preline UI -->
  <link rel="stylesheet" href="https://preline.co/assets/css/main.min.css">
  <script src="https://preline.co/assets/js/hs-ui.bundle.js"></script>
  <!-- Font Awesome for icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">  <!-- Chart.js for dashboard charts -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <!-- Chart.js datalabels plugin -->
  <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2"></script>
    <style>
    /* Background SVG styling */
    body {
      background-image: url('../../GiRa_background.svg');
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      background-attachment: fixed;
      min-height: 100vh;
    }

    .custom-scrollbar::-webkit-scrollbar {
      width: 6px;
    }
    .custom-scrollbar::-webkit-scrollbar-track {
      background: #374151;
    }
    .custom-scrollbar::-webkit-scrollbar-thumb {
      background: #6b7280;
      border-radius: 3px;
    }
    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
      background: #9ca3af;
    }

    /* Floating dock animations and glass morphism */
    #floating-dock {
      animation: slideUpFade 0.5s ease-out;
    }

    @keyframes slideUpFade {
      from {
        opacity: 0;
        transform: translate(-50%, 20px);
      }
      to {
        opacity: 1;
        transform: translate(-50%, 0);
      }
    }

    #floating-dock button {
      position: relative;
      overflow: hidden;
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    #floating-dock button::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      background: rgba(255, 255, 255, 0.15);
      border-radius: 50%;
      transform: translate(-50%, -50%);
      transition: all 0.3s ease;
    }

    #floating-dock button:hover::before {
      width: 100%;
      height: 100%;
      border-radius: 8px;
    }    /* Glass morphism effect for dock container */
    .glass-morphism {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.15);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    }    /* Enhanced glassmorphism effects for dashboard components */
    .glass-panel {
      background: rgba(31, 41, 55, 0.4);
      backdrop-filter: blur(15px);
      -webkit-backdrop-filter: blur(15px);
      border: 1px solid rgba(255, 255, 255, 0.08);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);
      transition: all 0.3s ease;
    }

    .glass-panel:hover {
      background: rgba(31, 41, 55, 0.5);
      border-color: rgba(255, 255, 255, 0.12);
      transform: translateY(-2px);
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.35);
    }

    .glass-card {
      background: rgba(55, 65, 81, 0.3);
      backdrop-filter: blur(12px);
      -webkit-backdrop-filter: blur(12px);
      border: 1px solid rgba(255, 255, 255, 0.05);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    }

    .glass-alert {
      background: rgba(31, 41, 55, 0.3);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.05);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    .glass-activity-item {
      background: rgba(55, 65, 81, 0.2);
      backdrop-filter: blur(8px);
      -webkit-backdrop-filter: blur(8px);
      border: 1px solid rgba(255, 255, 255, 0.03);
      border-radius: 8px;
      padding: 12px;
      margin-bottom: 8px;
      transition: all 0.3s ease;
    }    .glass-activity-item:hover {
      background: rgba(75, 85, 99, 0.3);
      border-color: rgba(255, 255, 255, 0.08);
      transform: translateX(4px);
    }

    /* Navbar glassmorphism styles */
    header.glass-panel {
      background: rgba(31, 41, 55, 0.2);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.05);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      position: sticky;
      top: 0;
      z-index: 40;
    }

    header.glass-panel:hover {
      background: rgba(31, 41, 55, 0.25);
      border-color: rgba(255, 255, 255, 0.08);
      transform: none; /* Prevent navbar movement on hover */
      box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
    }

    /* Navbar button glassmorphism */
    header .glass-card {
      background: rgba(55, 65, 81, 0.2);
      backdrop-filter: blur(8px);
      -webkit-backdrop-filter: blur(8px);
      border: 1px solid rgba(255, 255, 255, 0.05);
      transition: all 0.3s ease;
    }    header .glass-card:hover {
      background: rgba(75, 85, 99, 0.3);
      border-color: rgba(255, 255, 255, 0.1);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    /* Logo container styling */
    .logo-container {
      background: rgba(55, 65, 81, 0.15);
      backdrop-filter: blur(8px);
      -webkit-backdrop-filter: blur(8px);
      border: 1px solid rgba(255, 255, 255, 0.03);
      transition: all 0.3s ease;
    }

    .logo-container:hover {
      background: rgba(75, 85, 99, 0.25);
      border-color: rgba(255, 255, 255, 0.08);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .logo-container img {
      filter: brightness(0.9) contrast(1.1);
      transition: all 0.3s ease;
    }

    .logo-container:hover img {
      filter: brightness(1) contrast(1.2);
      transform: scale(1.05);
    }

    /* Dock icon styling */
    .dock-icon {
      width: 20px;
      height: 20px;
      stroke-width: 1.5;
      transition: all 0.3s ease;
    }

    #floating-dock button:hover .dock-icon {
      transform: scale(1.1);
      stroke-width: 2;
    }

    /* Dashboard card animations */
    .dashboard-card {
      transition: all 0.3s ease;
    }

    .dashboard-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    }

    /* Status indicator animations */
    .status-indicator {
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }

    /* Progress bar animations */
    .progress-bar {
      transition: width 1s ease-in-out;
    }

    /* Metric counter animation */
    .metric-counter {
      animation: countUp 1.5s ease-out;
    }

    @keyframes countUp {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }

    /* Chart container styling */
    .chart-container {
      position: relative;
      height: 300px;
    }

    /* Alert styling */
    .alert {
      border-left: 4px solid;
      animation: slideInLeft 0.5s ease-out;
    }    @keyframes slideInLeft {
      from { transform: translateX(-100%); opacity: 0; }
      to { transform: translateX(0); opacity: 1; }
    }

    /* CSV Table Styling */
    #csvDataTable {
      border-collapse: separate;
      border-spacing: 0;
    }

    #csvDataTable tbody tr {
      background: rgba(55, 65, 81, 0.2);
      backdrop-filter: blur(8px);
      border-bottom: 1px solid rgba(255, 255, 255, 0.05);
      transition: all 0.3s ease;
    }

    #csvDataTable tbody tr:hover {
      background: rgba(75, 85, 99, 0.4);
      transform: translateX(2px);
    }

    #csvDataTable tbody td {
      padding: 12px 8px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    }

    .status-badge {
      padding: 4px 8px;
      border-radius: 6px;
      font-size: 11px;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .status-red {
      background: rgba(239, 68, 68, 0.2);
      color: #fca5a5;
      border: 1px solid rgba(239, 68, 68, 0.3);
    }

    .status-yellow {
      background: rgba(245, 158, 11, 0.2);
      color: #fbbf24;
      border: 1px solid rgba(245, 158, 11, 0.3);
    }

    .status-green {
      background: rgba(34, 197, 94, 0.2);
      color: #86efac;
      border: 1px solid rgba(34, 197, 94, 0.3);
    }    .material-code {
      font-family: 'Courier New', monospace;
      background: rgba(55, 65, 81, 0.5);
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 11px;
    }

    /* Status color dots */
    .status-dot {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      display: inline-block;
      margin: 0 auto;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      transition: all 0.3s ease;
    }

    .status-dot:hover {
      transform: scale(1.2);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }

    .status-dot-red {
      background: linear-gradient(135deg, #ef4444, #dc2626);
      border: 2px solid rgba(239, 68, 68, 0.5);
    }

    .status-dot-yellow {
      background: linear-gradient(135deg, #f59e0b, #d97706);
      border: 2px solid rgba(245, 158, 11, 0.5);
    }

    .status-dot-green {
      background: linear-gradient(135deg, #22c55e, #16a34a);
      border: 2px solid rgba(34, 197, 94, 0.5);
    }    .status-dot-gray {
      background: linear-gradient(135deg, #6b7280, #4b5563);
      border: 2px solid rgba(107, 114, 128, 0.5);
    }

    /* Search Input Styling */
    #tableSearchInput {
      transition: all 0.3s ease;
    }

    #tableSearchInput:focus {
      background: rgba(55, 65, 81, 0.7) !important;
      transform: scale(1.02);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }

    #tableSearchInput::placeholder {
      transition: color 0.3s ease;
    }

    #tableSearchInput:focus::placeholder {
      color: rgba(156, 163, 175, 0.8);
    }

    /* Search Results Styling */
    .search-highlight {
      background: rgba(59, 130, 246, 0.3);
      padding: 1px 2px;
      border-radius: 2px;
    }
  </style>
</head>

<body class="bg-gray-900 text-white overflow-x-hidden">
  <!-- Main Container -->  <div class="min-h-screen">    <!-- Header -->    <header class="glass-panel border-b border-gray-700/50 px-6 py-4 backdrop-blur-xl">
      <div class="flex items-center justify-between">
        <!-- Left Section - Title -->
        <div class="flex items-center space-x-4">
          <div>
            <h1 class="text-2xl font-bold text-white">SC Operator KI</h1>
          </div>
        </div>
        
        <!-- Center Section - Mercedes Logo -->
        <div class="absolute left-1/2 transform -translate-x-1/2">
          <div class="">
            <img src="../assets/media/logo/mb_star.png" alt="Mercedes-Benz" class="h-10 w-10 opacity-90 hover:opacity-100 transition-all duration-300 hover:scale-110">
          </div>
        </div>
        
        <!-- Right Section - Company Logos -->
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-3">
            <!-- MO PSCA Logo -->
            <div class="">
              <img src="../assets/media/logo/mo_psca.png" alt="MO PSCA" class="h-8 w-auto opacity-90 hover:opacity-100 transition-opacity duration-200" onerror="console.log('Failed to load MO PSCA logo:', this.src, window.location.href); this.style.display='none';">
            </div>
            <!-- MO HUB Logo -->
            <div class="">
              <img src="../assets/media/logo/mo_hub.png" alt="MO HUB" class="h-8 w-auto opacity-90 hover:opacity-100 transition-opacity duration-200" onerror="console.log('Failed to load MO HUB logo:', this.src, window.location.href); this.style.display='none';">
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="p-6 custom-scrollbar overflow-y-auto" style="height: calc(100vh - 80px);">
      <!-- Quick Stats Row -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- System Status -->
        <div class="dashboard-card bg-gradient-to-br from-blue-600 to-blue-800 rounded-xl p-6 text-white">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-blue-100 text-sm font-medium">System Status</p>
              <p class="text-2xl font-bold mt-1">Online</p>
              <div class="flex items-center mt-2">
                <div class="status-indicator w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                <span class="text-blue-100 text-xs">All systems operational</span>
              </div>
            </div>
            <i class="fas fa-server text-blue-200 text-2xl"></i>
          </div>
        </div>

        <!-- CPU Usage -->
        <div class="dashboard-card bg-gradient-to-br from-green-600 to-green-800 rounded-xl p-6 text-white">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <p class="text-green-100 text-sm font-medium">CPU Usage</p>
              <p class="text-2xl font-bold mt-1 metric-counter" id="cpu-usage">45%</p>
              <div class="w-full bg-green-900 rounded-full h-2 mt-3">
                <div class="progress-bar bg-green-400 h-2 rounded-full" style="width: 45%"></div>
              </div>
            </div>
            <i class="fas fa-microchip text-green-200 text-2xl ml-4"></i>
          </div>
        </div>

        <!-- Memory Usage -->
        <div class="dashboard-card bg-gradient-to-br from-purple-600 to-purple-800 rounded-xl p-6 text-white">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <p class="text-purple-100 text-sm font-medium">Memory Usage</p>
              <p class="text-2xl font-bold mt-1 metric-counter" id="memory-usage">68%</p>
              <div class="w-full bg-purple-900 rounded-full h-2 mt-3">
                <div class="progress-bar bg-purple-400 h-2 rounded-full" style="width: 68%"></div>
              </div>
            </div>
            <i class="fas fa-memory text-purple-200 text-2xl ml-4"></i>
          </div>
        </div>

        <!-- Active Users -->
        <div class="dashboard-card bg-gradient-to-br from-orange-600 to-orange-800 rounded-xl p-6 text-white">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-orange-100 text-sm font-medium">Active Users</p>
              <p class="text-2xl font-bold mt-1 metric-counter" id="active-users">1,247</p>
              <div class="flex items-center mt-2">
                <i class="fas fa-arrow-up text-orange-300 text-xs mr-1"></i>
                <span class="text-orange-100 text-xs">+12% from yesterday</span>
              </div>
            </div>
            <i class="fas fa-users text-orange-200 text-2xl"></i>
          </div>
        </div>      </div>

      <!-- CSV Data Table Section -->
      <div class="mb-8">
        <div class="dashboard-card glass-panel rounded-xl p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-xl font-semibold text-white flex items-center">
              <i class="fas fa-table text-blue-500 mr-3"></i>
              Material Management Data
            </h3>            <div class="flex items-center space-x-3">              <!-- Search Input -->
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <i class="fas fa-search text-gray-400 text-sm"></i>
                </div>
                <input 
                  type="text" 
                  id="tableSearchInput"
                  placeholder="Search materials..." 
                  class="glass-card text-white text-sm rounded-md pl-10 pr-4 py-2 w-64 border-0 focus:ring-2 focus:ring-blue-500 focus:outline-none placeholder-gray-400"
                  style="background: rgba(55, 65, 81, 0.5);"
                >
              </div>
            </div>
          </div>
            <div class="overflow-x-auto custom-scrollbar">
            <div id="csvTableLoading" class="text-center py-8">
              <div class="inline-flex items-center">
                <i class="fas fa-spinner fa-spin text-blue-500 text-xl mr-3"></i>
                <span class="text-gray-300">Loading data...</span>
              </div>
            </div>            <table class="w-full text-sm hidden" id="csvDataTable">
              <thead>
                <tr class="border-b border-gray-600/50">
                  <th class="text-center py-3 px-2 text-gray-300 font-medium">Material</th>
                  <th class="text-center py-3 px-2 text-gray-300 font-medium">Material Short Text</th>
                  <th class="text-center py-3 px-2 text-gray-300 font-medium">Stock</th>
                  <th class="text-center py-3 px-2 text-gray-300 font-medium">Start</th>
                  <th class="text-center py-3 px-2 text-gray-300 font-medium">Actual</th>
                  <th class="text-center py-3 px-2 text-gray-300 font-medium">Forecast</th>
                  <th class="text-center py-3 px-2 text-gray-300 font-medium">Avise</th>
                  <th class="text-center py-3 px-2 text-gray-300 font-medium">Status</th>
                </tr>
              </thead>
              <tbody id="csvTableBody">
                <!-- Data will be populated by JavaScript -->
              </tbody>
            </table>
          </div>
        </div>
      </div>      <!-- Top Charts Row - Material Status Overview and Coverage Under One Day -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Performance Chart -->
        <div class="dashboard-card glass-panel rounded-xl p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-white">Material Status Overview</h3>
          </div>
          <div class="chart-container">
            <canvas id="performanceChart"></canvas>
          </div>
        </div>
          <!-- Coverage Under One Day Chart -->
        <div class="dashboard-card glass-panel rounded-xl p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-white">Coverage Under One Day</h3>
          </div>
          <div class="chart-container">
            <canvas id="coverageChart"></canvas>
          </div>
        </div>
      </div>

      <!-- System Information & Alerts Row -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">        <!-- Recent Activity -->
        <div class="dashboard-card glass-panel rounded-xl p-6">
          <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
            <i class="fas fa-clock text-green-500 mr-2"></i>
            Recent Activity
          </h3>
          <div class="space-y-3">
            <div class="glass-activity-item">
              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <p class="text-white text-sm">Email client accessed</p>
                  <p class="text-gray-400 text-xs">2 minutes ago</p>
                </div>
              </div>
            </div>
            <div class="glass-activity-item">
              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <p class="text-white text-sm">API endpoint called</p>
                  <p class="text-gray-400 text-xs">5 minutes ago</p>
                </div>
              </div>
            </div>
            <div class="glass-activity-item">
              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-yellow-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <p class="text-white text-sm">System resources checked</p>
                  <p class="text-gray-400 text-xs">8 minutes ago</p>
                </div>
              </div>
            </div>
            <div class="glass-activity-item">
              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <p class="text-white text-sm">Dashboard loaded</p>
                  <p class="text-gray-400 text-xs">12 minutes ago</p>
                </div>
              </div>
            </div>
          </div>
        </div>        

        <!-- Status Change -->
        <div class="dashboard-card glass-panel rounded-xl p-6">
          <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
            <i class="fas fa-exchange-alt text-orange-500 mr-2"></i>
            Status Change Information
          </h3>
          <div class="space-y-3">
            <div class="glass-activity-item">
              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <p class="text-white text-sm">Material A001 changed to Red</p>
                  <p class="text-gray-400 text-xs">1 minute ago</p>
                </div>
              </div>
            </div>
            <div class="glass-activity-item">
              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-yellow-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <p class="text-white text-sm">Material B205 changed to Yellow</p>
                  <p class="text-gray-400 text-xs">3 minutes ago</p>
                </div>
              </div>
            </div>
            <div class="glass-activity-item">
              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <p class="text-white text-sm">Material C110 changed to Green</p>
                  <p class="text-gray-400 text-xs">7 minutes ago</p>
                </div>
              </div>
            </div>
            <div class="glass-activity-item">
              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <p class="text-white text-sm">Material D330 status updated</p>
                  <p class="text-gray-400 text-xs">15 minutes ago</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Discrepancy Information -->
        <div class="dashboard-card glass-panel rounded-xl p-6">
          <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
            <i class="fas fa-exclamation-circle text-red-500 mr-2"></i>
            Discrepancy Information
          </h3>
          <div class="space-y-3">
            <div class="glass-activity-item">
              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <p class="text-white text-sm">Stock mismatch detected: E102</p>
                  <p class="text-gray-400 text-xs">4 minutes ago</p>
                </div>
              </div>
            </div>
            <div class="glass-activity-item">
              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-yellow-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <p class="text-white text-sm">Forecast variance: F205</p>
                  <p class="text-gray-400 text-xs">6 minutes ago</p>
                </div>
              </div>
            </div>
            <div class="glass-activity-item">
              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <p class="text-white text-sm">Coverage calculation error: G450</p>
                  <p class="text-gray-400 text-xs">9 minutes ago</p>
                </div>
              </div>
            </div>
            <div class="glass-activity-item">
              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <p class="text-white text-sm">Data sync issue resolved: H123</p>
                  <p class="text-gray-400 text-xs">18 minutes ago</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>

  <!-- Floating Dock -->
  <div id="floating-dock" class="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50">
    <div class="glass-morphism rounded-xl px-3 py-2 shadow-2xl">
      <div class="flex items-center space-x-3">
        <!-- Home/Main Page -->
        <button onclick="goToMainPage()" class="group flex items-center justify-center w-10 h-10 rounded-lg bg-gray-700/60 hover:bg-blue-600/80 transition-all duration-200 hover:scale-105 relative overflow-hidden" title="Main Page">
          <svg xmlns="http://www.w3.org/2000/svg" class="dock-icon text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
          </svg>
        </button>
        
        <!-- Cockpit/Dashboard -->
        <button onclick="openCockpit()" class="group flex items-center justify-center w-10 h-10 rounded-lg bg-gray-700/60 hover:bg-purple-600/80 transition-all duration-200 hover:scale-105 relative overflow-hidden" title="Cockpit Dashboard">
          <svg xmlns="http://www.w3.org/2000/svg" class="dock-icon text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 6a7.5 7.5 0 1 0 7.5 7.5h-7.5V6Z" />
            <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 10.5H21A7.5 7.5 0 0 0 13.5 3v7.5Z" />
          </svg>
        </button>

        <!-- Email Client -->
        <button onclick="openEmailClient()" class="group flex items-center justify-center w-10 h-10 rounded-lg bg-gray-700/60 hover:bg-gray-600/80 transition-all duration-200 hover:scale-105 relative overflow-hidden" title="Email Client">
          <svg xmlns="http://www.w3.org/2000/svg" class="dock-icon text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75" />
          </svg>
        </button>
      </div>
    </div>
  </div>
  <!-- Scripts -->
  <script src="../assets/js/python.js"></script>
  <script src="../assets/js/cockpit.js"></script>
  
  <!-- Logo Loading Debug Script -->
  <script>
    // Debug logo loading
    document.addEventListener('DOMContentLoaded', function() {
      console.log('Current location:', window.location.href);
      console.log('Base URL:', document.baseURI);
      
      // Try multiple path approaches for logos
      const logoPaths = [
        '../assets/media/logo/',
        './assets/media/logo/',
        'assets/media/logo/',
        '../../public/assets/media/logo/',
        '/assets/media/logo/'
      ];
      
      const logoFiles = ['mo_psca.png', 'mo_hub.png'];
      
      logoFiles.forEach((filename, index) => {
        const imgElement = document.querySelectorAll('.logo-container img')[index];
        if (imgElement) {
          let pathIndex = 0;
          
          function tryNextPath() {
            if (pathIndex < logoPaths.length) {
              const testPath = logoPaths[pathIndex] + filename;
              console.log(`Trying path ${pathIndex + 1}/${logoPaths.length} for ${filename}:`, testPath);
              
              // Test if image exists
              const testImg = new Image();
              testImg.onload = function() {
                console.log(`✓ Success: ${testPath} loaded`);
                imgElement.src = testPath;
                imgElement.style.display = 'block';
              };
              testImg.onerror = function() {
                console.log(`✗ Failed: ${testPath}`);
                pathIndex++;
                tryNextPath();
              };
              testImg.src = testPath;
            } else {
              console.log(`❌ All paths failed for ${filename}`);
              // Show placeholder or fallback
              imgElement.style.display = 'none';
            }
          }
          
          tryNextPath();
        }
      });
    });
  </script>
</body>

</html>
