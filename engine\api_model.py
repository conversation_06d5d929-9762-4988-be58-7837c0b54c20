from typing import Optional, List, Dict, Any
from pydantic import BaseModel

class PathModel(BaseModel):
    path: str
    message: Optional[str]

class HelloModel(BaseModel):
    name: str
    message: Optional[str]

class EmailSendModel(BaseModel):
    to: str
    subject: str
    body: str
    cc: Optional[str] = None
    attachments: Optional[list[str]] = None

class EmailReceiveModel(BaseModel):
    subject: Optional[str]
    sender: Optional[str]
    body: Optional[str]
    received_time: Optional[str]

class MaterialModel(BaseModel):
    rule: str
    material: str
    material_short_text: str
    result_description: str
    sap_range: float
    stock: int
    status: str
    coverage_status: str
    coverage_days: str

class CriticalMaterialModel(BaseModel):
    materials: List[MaterialModel]

class EmailCompositionModel(BaseModel):
    stakeholder: str
    email_address: str
    subject: str
    body: str
    material_count: int

class IncomingEmailModel(BaseModel):
    subject: Optional[str]
    sender: Optional[str]
    body: Optional[str]
    received_time: Optional[str]
    stakeholder_type: Optional[str]
    processed: bool = False

class EmailAnalysisModel(BaseModel):
    email_id: str
    extracted_info: Dict[str, Any]
    analysis_timestamp: str
    confidence_score: float

class CockpitStatsModel(BaseModel):
    total_materials: int
    red_status_count: int
    yellow_status_count: int
    green_status_count: int
    critical_materials_count: int
    emails_sent_count: int
    emails_received_count: int
    last_updated: str

class AzureOpenAIConfigModel(BaseModel):
    endpoint: str
    api_key: str
    deployment_name: str
    api_version: str = "2024-02-01"
